"""Basic tests for the migration tool."""

import pytest
import asyncio
from unittest.mock import Mock, patch
from migration.config import Config
from migration.transformer.product_mapper import ProductMapper

class TestConfig:
    """Test configuration management."""
    
    def test_config_creation(self):
        """Test that config can be created."""
        config = Config()
        assert config.salla is not None
        assert config.shopify is not None
        assert config.migration is not None
        assert config.paths is not None

class TestProductMapper:
    """Test product data mapping."""
    
    def setup_method(self):
        """Setup test data."""
        self.mapper = ProductMapper()
        
        # Sample Salla.sa product
        self.salla_product = {
            "id": "123",
            "name": "Test Product",
            "description": "A test product description",
            "price": 100.00,
            "sale_price": 80.00,
            "sku": "TEST-SKU-001",
            "quantity": 10,
            "status": "active",
            "categories": ["cat1", "cat2"],
            "brand_id": "brand1",
            "images": [
                {"url": "https://example.com/image1.jpg", "alt": "Image 1"},
                {"url": "https://example.com/image2.jpg", "alt": "Image 2"}
            ],
            "variants": [
                {
                    "id": "var1",
                    "name": "Small",
                    "price": 100.00,
                    "sale_price": 80.00,
                    "sku": "TEST-SKU-001-S",
                    "quantity": 5
                },
                {
                    "id": "var2", 
                    "name": "Large",
                    "price": 120.00,
                    "sale_price": 100.00,
                    "sku": "TEST-SKU-001-L",
                    "quantity": 5
                }
            ],
            "seo": {
                "title": "Test Product SEO Title",
                "description": "Test Product SEO Description"
            }
        }
        
        # Sample categories and brands
        self.categories = [
            {"id": "cat1", "name": "Category 1"},
            {"id": "cat2", "name": "Category 2"}
        ]
        
        self.brands = [
            {"id": "brand1", "name": "Test Brand"}
        ]
    
    def test_generate_handle(self):
        """Test handle generation."""
        handle = self.mapper.generate_handle("Test Product Name!")
        assert handle == "test-product-name"
        
        handle = self.mapper.generate_handle("Product with   Multiple    Spaces")
        assert handle == "product-with-multiple-spaces"
    
    def test_map_status(self):
        """Test status mapping."""
        assert self.mapper.map_status("active") == "active"
        assert self.mapper.map_status("inactive") == "draft"
        assert self.mapper.map_status("unknown") == "draft"
    
    def test_map_product_type(self):
        """Test product type mapping."""
        self.mapper.set_category_mapping(self.categories)
        
        product_type = self.mapper.map_product_type(["cat1", "cat2"])
        assert product_type == "Category 1"
        
        product_type = self.mapper.map_product_type([])
        assert product_type == "General"
    
    def test_map_variants(self):
        """Test variant mapping."""
        variants = self.mapper.map_variants(self.salla_product)
        
        assert len(variants) == 2
        assert variants[0]["title"] == "Small"
        assert variants[0]["price"] == "80.0"  # Sale price
        assert variants[0]["compare_at_price"] == "100.0"  # Original price
        assert variants[0]["sku"] == "TEST-SKU-001-S"
        assert variants[0]["inventory_quantity"] == 5
    
    def test_map_images(self):
        """Test image mapping."""
        images = self.mapper.map_images(self.salla_product["images"])
        
        assert len(images) == 2
        assert images[0]["src"] == "https://example.com/image1.jpg"
        assert images[0]["alt"] == "Image 1"
    
    def test_map_complete_product(self):
        """Test complete product mapping."""
        self.mapper.set_category_mapping(self.categories)
        self.mapper.set_brand_mapping(self.brands)
        
        shopify_product = self.mapper.map_product(self.salla_product)
        
        assert "product" in shopify_product
        product = shopify_product["product"]
        
        assert product["title"] == "Test Product"
        assert product["body_html"] == "A test product description"
        assert product["vendor"] == "Test Brand"
        assert product["product_type"] == "Category 1"
        assert product["handle"] == "test-product"
        assert product["status"] == "active"
        assert len(product["variants"]) == 2
        assert len(product["images"]) == 2
        assert product["seo_title"] == "Test Product SEO Title"
        assert product["seo_description"] == "Test Product SEO Description"

@pytest.mark.asyncio
class TestAsyncComponents:
    """Test async components."""
    
    async def test_rate_limiter(self):
        """Test rate limiter."""
        from migration.utils.rate_limiter import RateLimiter
        
        limiter = RateLimiter(requests_per_second=2.0)
        
        import time
        start_time = time.time()
        
        await limiter.acquire()
        await limiter.acquire()
        
        elapsed = time.time() - start_time
        # Should take at least 0.5 seconds for 2 requests at 2 req/sec
        assert elapsed >= 0.4  # Allow some tolerance

if __name__ == "__main__":
    pytest.main([__file__])
