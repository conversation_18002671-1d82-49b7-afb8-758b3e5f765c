require('dotenv').config();

const config = {
  // Salla.sa Configuration
  salla: {
    baseUrl: 'https://api.salla.dev/admin/v2',
    clientId: process.env.SALLA_CLIENT_ID,
    clientSecret: process.env.SALLA_CLIENT_SECRET,
    accessToken: process.env.SALLA_ACCESS_TOKEN,
    storeUrl: process.env.SALLA_STORE_URL,
    rateLimit: {
      requestsPerHour: 1000,
      requestsPerSecond: 1
    }
  },

  // Shopify Configuration
  shopify: {
    shopName: process.env.SHOPIFY_SHOP_NAME,
    baseUrl: `https://${process.env.SHOPIFY_SHOP_NAME}.myshopify.com/admin/api/2023-10`,
    accessToken: process.env.SHOPIFY_ACCESS_TOKEN,
    apiVersion: '2023-10',
    rateLimit: {
      requestsPerSecond: 2,
      bulkOperationsLimit: 1000
    }
  },

  // Migration Settings
  migration: {
    batchSize: 50,
    maxRetries: 3,
    retryDelay: 1000,
    imageDownloadTimeout: 30000,
    enableImageMigration: true,
    enableVariantMigration: true,
    enableCategoryMigration: true,
    testMode: process.env.NODE_ENV !== 'production'
  },

  // File Paths
  paths: {
    dataDir: './data',
    exportDir: './data/exports',
    importDir: './data/imports',
    imagesDir: './data/images',
    logsDir: './logs',
    backupDir: './data/backups'
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: './logs/migration.log',
    maxFiles: 5,
    maxSize: '10m'
  }
};

// Validation
const requiredEnvVars = [
  'SALLA_ACCESS_TOKEN',
  'SHOPIFY_SHOP_NAME',
  'SHOPIFY_ACCESS_TOKEN'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingVars.length > 0) {
  console.warn(`Warning: Missing environment variables: ${missingVars.join(', ')}`);
  console.warn('Please create a .env file with the required variables.');
}

module.exports = config;
