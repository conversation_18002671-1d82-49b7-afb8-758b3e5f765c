#!/usr/bin/env python3
"""
Quick start script for Salla.sa to Shopify migration.
This script provides an easy way to run the migration without using the CLI.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from migration.config import config
from migration.utils.logger import get_logger, setup_logger
from migration.salla.exporter import SallaExporter
from migration.transformer.product_mapper import ProductMapper
from migration.shopify.importer import ShopifyImporter

# Setup logging
setup_logger("migration")
logger = get_logger("run_migration")

async def main():
    """Run the complete migration process."""

    print("🚀 Salla.sa to Shopify Migration Tool")
    print("=" * 50)

    # Determine export method
    export_method = "api"  # Default
    store_url = config.salla.store_url

    # If no API token but store URL exists, use scraping
    if not config.salla.access_token and store_url:
        export_method = "scrape"
        print("🕷️  No API token found, using web scraping method")
    elif config.salla.access_token:
        print("🔑 API token found, using API method")

    # Check configuration based on method
    if export_method == "api":
        errors = config.validate()
        if errors:
            print("❌ API Configuration errors:")
            for error in errors:
                print(f"  - {error}")

            # Fallback to scraping if store URL is available
            if store_url:
                print(f"\n💡 Falling back to web scraping method using: {store_url}")
                export_method = "scrape"
            else:
                print("\nPlease create a .env file with your API credentials or store URL.")
                print("See .env.example for the required format.")
                return
    elif export_method == "scrape" and not store_url:
        print("❌ Store URL is required for scraping method")
        print("Please set SALLA_STORE_URL in your .env file")
        return

    print(f"✅ Configuration valid for {export_method} method")
    print(f"📁 Data directory: {config.paths.data_dir}")
    print(f"🧪 Test mode: {config.migration.test_mode}")
    print()

    try:
        # Step 1: Export from Salla.sa
        print(f"📤 Step 1/3: Exporting data from Salla.sa using {export_method} method...")

        if export_method == "scrape":
            from migration.salla.scraper_exporter import SallaScraperExporter
            exporter = SallaScraperExporter(store_url)
            export_result = await exporter.export_all(download_images=True)
        else:
            from migration.salla.exporter import SallaExporter
            exporter = SallaExporter()
            export_result = await exporter.export_all(download_images=True)
        
        print(f"✅ Export completed:")
        print(f"   - Products: {export_result['summary']['totals']['products']}")
        print(f"   - Categories: {export_result['summary']['totals']['categories']}")
        print(f"   - Brands: {export_result['summary']['totals']['brands']}")
        print()
        
        # Step 2: Transform data
        print("🔄 Step 2/3: Transforming data for Shopify...")
        mapper = ProductMapper()
        transformed_products = mapper.map_products(
            export_result["data"]["products"],
            export_result["data"]["categories"],
            export_result["data"]["brands"]
        )
        
        print(f"✅ Transformation completed:")
        print(f"   - Transformed products: {len(transformed_products)}")
        print()
        
        # Step 3: Import to Shopify
        print("📥 Step 3/3: Importing to Shopify...")
        
        if config.migration.test_mode:
            print("🧪 TEST MODE: Simulating import (no actual changes to Shopify)")
            print(f"   - Would import {len(transformed_products)} products")
            
            # Show sample of products that would be imported
            print("\n📋 Sample products to import:")
            for i, product in enumerate(transformed_products[:5]):
                title = product["product"]["title"]
                variants = len(product["product"]["variants"])
                images = len(product["product"]["images"])
                print(f"   {i+1}. {title} ({variants} variants, {images} images)")
            
            if len(transformed_products) > 5:
                print(f"   ... and {len(transformed_products) - 5} more products")
            
            print("\n💡 To run actual import, set MIGRATION_TEST_MODE=false in .env")
            
        else:
            print("⚠️  PRODUCTION MODE: This will create products in your Shopify store!")
            response = input("Continue? (y/N): ")
            
            if response.lower() != 'y':
                print("❌ Import cancelled by user")
                return
            
            importer = ShopifyImporter()
            import_result = await importer.import_products(transformed_products)
            
            print(f"✅ Import completed:")
            print(f"   - Successfully imported: {import_result['successful']}")
            print(f"   - Failed imports: {import_result['failed']}")
        
        print("\n🎉 Migration process completed!")
        print("=" * 50)
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        print(f"❌ Migration failed: {str(e)}")
        print("Check the logs for more details.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
