#!/usr/bin/env python3
"""
Quick test script for web scraping functionality.
This script tests the scraping capabilities on your Salla.sa store.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from migration.salla.scraper import Salla<PERSON>craper
from migration.utils.logger import setup_logger, get_logger

# Setup logging
setup_logger("scrape_test")
logger = get_logger("scrape_test")

async def test_scraping():
    """Test the scraping functionality."""
    
    # Your store URL - update this to your actual store
    store_url = "https://chrisbella-sa.com"
    
    print("🕷️  Salla.sa Web Scraping Test")
    print("=" * 50)
    print(f"Testing store: {store_url}")
    print()
    
    try:
        async with SallaScraper(store_url) as scraper:
            print("🔍 Step 1: Discovering product URLs...")
            
            # Discover product URLs (limit to first few for testing)
            product_urls = await scraper.discover_product_urls()
            
            if not product_urls:
                print("❌ No product URLs found!")
                print("This could mean:")
                print("  - The store URL is incorrect")
                print("  - The store has no products")
                print("  - The store structure is different than expected")
                return
            
            print(f"✅ Found {len(product_urls)} product URLs")
            print("\n📋 Sample product URLs:")
            for i, url in enumerate(product_urls[:5], 1):
                print(f"  {i}. {url}")
            
            if len(product_urls) > 5:
                print(f"  ... and {len(product_urls) - 5} more")
            
            print(f"\n🛍️  Step 2: Testing product scraping...")
            
            # Test scraping first few products
            test_products = []
            for i, url in enumerate(product_urls[:3], 1):
                print(f"  Scraping product {i}/3: {url}")
                
                product_data = await scraper.scrape_product(url)
                if product_data:
                    test_products.append(product_data)
                    print(f"    ✅ Success: {product_data.get('name', 'Unknown')}")
                    print(f"       Price: {product_data.get('price', 'N/A')}")
                    print(f"       Images: {len(product_data.get('images', []))}")
                    print(f"       Categories: {product_data.get('categories', [])}")
                else:
                    print(f"    ❌ Failed to scrape")
            
            print(f"\n📊 Scraping Test Results:")
            print(f"  Total URLs found: {len(product_urls)}")
            print(f"  Successfully scraped: {len(test_products)}")
            print(f"  Success rate: {len(test_products)/min(3, len(product_urls))*100:.1f}%")
            
            if test_products:
                print(f"\n✅ Scraping test successful!")
                print(f"You can now run the full migration with:")
                print(f"  python run_migration.py")
                print(f"  OR")
                print(f"  python -m migration.cli export --method scrape --store-url {store_url}")
            else:
                print(f"\n❌ Scraping test failed!")
                print(f"Check the logs for more details.")
    
    except Exception as e:
        logger.error(f"Scraping test failed: {str(e)}")
        print(f"❌ Test failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    print("Starting scraping test...")
    exit_code = asyncio.run(test_scraping())
    sys.exit(exit_code)
