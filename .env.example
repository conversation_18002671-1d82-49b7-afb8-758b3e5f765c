# Salla.sa API Configuration
SALLA_CLIENT_ID=your_salla_client_id
SALLA_CLIENT_SECRET=your_salla_client_secret
SALLA_ACCESS_TOKEN=your_salla_access_token
SALLA_STORE_URL=https://your-store.salla.sa

# Shopify API Configuration
SHOPIFY_SHOP_NAME=your-shop-name
SHOPIFY_ACCESS_TOKEN=your_shopify_access_token

# Migration Settings
MIGRATION_BATCH_SIZE=50
MIGRATION_MAX_RETRIES=3
MIGRATION_ENABLE_IMAGE_MIGRATION=true
MIGRATION_TEST_MODE=true

# Environment & Logging
NODE_ENV=development
LOG_LEVEL=INFO
