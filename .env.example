# Salla.sa API Configuration
SALLA_CLIENT_ID=your_salla_client_id
SALLA_CLIENT_SECRET=your_salla_client_secret
SALLA_ACCESS_TOKEN=your_salla_access_token
SALLA_STORE_URL=https://your-store.salla.sa

# Shopify API Configuration
SHOPIFY_SHOP_NAME=your-shop-name
SHOPIFY_ACCESS_TOKEN=your_shopify_access_token

# Environment
NODE_ENV=development
LOG_LEVEL=info

# Optional: Database (if needed for tracking)
# DATABASE_URL=postgresql://user:password@localhost:5432/migration_db
