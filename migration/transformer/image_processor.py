"""Image processing utilities for migration."""

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from ..config import config
from ..utils.logger import get_logger
from ..utils.image_utils import download_product_images, validate_image

logger = get_logger("image_processor")

class ImageProcessor:
    """Process and prepare images for Shopify import."""
    
    def __init__(self):
        self.processed_images = {}
        self.failed_downloads = []
    
    async def process_product_images(self, product_id: str, salla_images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Process all images for a product."""
        if not salla_images or not config.migration.enable_image_migration:
            return []
        
        logger.info(f"Processing {len(salla_images)} images for product {product_id}")
        
        # Extract image URLs
        image_urls = []
        for img in salla_images:
            url = img.get("url")
            if url:
                image_urls.append(url)
        
        if not image_urls:
            logger.warning(f"No valid image URLs found for product {product_id}")
            return []
        
        # Download images
        try:
            downloaded_images = await download_product_images(
                product_id, 
                image_urls, 
                config.paths.images_dir
            )
            
            # Process downloaded images for Shopify
            shopify_images = []
            for i, downloaded_img in enumerate(downloaded_images):
                original_img = salla_images[i] if i < len(salla_images) else {}
                
                shopify_image = {
                    "src": downloaded_img["original_url"],  # Use original URL for now
                    "alt": original_img.get("alt", f"Product image {i+1}"),
                    "position": i + 1
                }
                
                # Add metadata about local file
                shopify_image["metafields"] = [
                    {
                        "namespace": "salla_migration",
                        "key": "local_file",
                        "value": downloaded_img["filename"],
                        "type": "single_line_text_field"
                    }
                ]
                
                shopify_images.append(shopify_image)
            
            self.processed_images[product_id] = shopify_images
            logger.info(f"Successfully processed {len(shopify_images)} images for product {product_id}")
            
            return shopify_images
            
        except Exception as e:
            logger.error(f"Failed to process images for product {product_id}: {str(e)}")
            self.failed_downloads.append({
                "product_id": product_id,
                "error": str(e),
                "image_count": len(image_urls)
            })
            return []
    
    async def batch_process_images(self, products: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Process images for multiple products in batches."""
        logger.info(f"Starting batch image processing for {len(products)} products")
        
        # Create tasks for all products
        tasks = []
        for product in products:
            product_id = product.get("id", "unknown")
            images = product.get("images", [])
            
            if images:
                task = self.process_product_images(product_id, images)
                tasks.append((product_id, task))
        
        # Process in batches to avoid overwhelming the system
        batch_size = 10
        all_results = {}
        
        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i:i + batch_size]
            logger.info(f"Processing image batch {i//batch_size + 1}/{(len(tasks) + batch_size - 1)//batch_size}")
            
            # Execute batch
            batch_results = await asyncio.gather(
                *[task for _, task in batch_tasks],
                return_exceptions=True
            )
            
            # Store results
            for j, (product_id, _) in enumerate(batch_tasks):
                result = batch_results[j]
                if isinstance(result, Exception):
                    logger.error(f"Image processing failed for product {product_id}: {str(result)}")
                    all_results[product_id] = []
                else:
                    all_results[product_id] = result
            
            # Small delay between batches
            if i + batch_size < len(tasks):
                await asyncio.sleep(1)
        
        logger.info(f"Batch image processing completed. Processed {len(all_results)} products")
        return all_results
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Get summary of image processing results."""
        total_products = len(self.processed_images)
        total_images = sum(len(images) for images in self.processed_images.values())
        failed_products = len(self.failed_downloads)
        
        return {
            "total_products_processed": total_products,
            "total_images_processed": total_images,
            "failed_products": failed_products,
            "success_rate": round((total_products / (total_products + failed_products)) * 100, 1) if (total_products + failed_products) > 0 else 0,
            "failed_downloads": self.failed_downloads
        }
    
    def validate_processed_images(self) -> Dict[str, Any]:
        """Validate all processed images."""
        logger.info("Validating processed images...")
        
        validation_results = {
            "valid_images": 0,
            "invalid_images": 0,
            "missing_files": 0,
            "details": []
        }
        
        for product_id, images in self.processed_images.items():
            for image in images:
                # Check if local file exists and is valid
                local_filename = None
                for metafield in image.get("metafields", []):
                    if metafield.get("key") == "local_file":
                        local_filename = metafield.get("value")
                        break
                
                if local_filename:
                    local_path = config.paths.images_dir / local_filename
                    
                    if local_path.exists():
                        if validate_image(local_path):
                            validation_results["valid_images"] += 1
                        else:
                            validation_results["invalid_images"] += 1
                            validation_results["details"].append({
                                "product_id": product_id,
                                "file": local_filename,
                                "issue": "Invalid image file"
                            })
                    else:
                        validation_results["missing_files"] += 1
                        validation_results["details"].append({
                            "product_id": product_id,
                            "file": local_filename,
                            "issue": "File not found"
                        })
        
        logger.info(f"Image validation completed: {validation_results['valid_images']} valid, "
                   f"{validation_results['invalid_images']} invalid, "
                   f"{validation_results['missing_files']} missing")
        
        return validation_results
