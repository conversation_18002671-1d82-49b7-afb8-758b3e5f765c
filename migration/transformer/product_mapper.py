"""Product data mapping from Salla.sa to Shopify format."""

import re
from typing import Dict, List, Any, Optional
from ..config import config
from ..utils.logger import get_logger

logger = get_logger("product_mapper")

class ProductMapper:
    """Maps Salla.sa product data to Shopify format."""
    
    def __init__(self):
        self.category_mapping = {}
        self.brand_mapping = {}
    
    def set_category_mapping(self, categories: List[Dict[str, Any]]) -> None:
        """Set category ID to name mapping."""
        self.category_mapping = {cat.get("id"): cat.get("name") for cat in categories}
    
    def set_brand_mapping(self, brands: List[Dict[str, Any]]) -> None:
        """Set brand ID to name mapping."""
        self.brand_mapping = {brand.get("id"): brand.get("name") for brand in brands}
    
    def generate_handle(self, title: str) -> str:
        """Generate Shopify handle from product title."""
        # Convert to lowercase and replace spaces/special chars with hyphens
        handle = re.sub(r'[^\w\s-]', '', title.lower())
        handle = re.sub(r'[-\s]+', '-', handle)
        return handle.strip('-')
    
    def map_status(self, salla_status: str) -> str:
        """Map Salla.sa status to Shopify status."""
        status_mapping = {
            "active": "active",
            "inactive": "draft",
            "out_of_stock": "active",  # Keep active but set inventory to 0
            "draft": "draft"
        }
        return status_mapping.get(salla_status, "draft")
    
    def map_product_type(self, categories: List[str]) -> str:
        """Map categories to Shopify product type."""
        if not categories:
            return "General"
        
        # Use the first category as product type
        first_category_id = categories[0] if isinstance(categories[0], str) else categories[0].get("id")
        return self.category_mapping.get(first_category_id, "General")
    
    def map_collections(self, categories: List[str]) -> List[str]:
        """Map categories to Shopify collections."""
        collections = []
        for category in categories:
            category_id = category if isinstance(category, str) else category.get("id")
            category_name = self.category_mapping.get(category_id)
            if category_name:
                collections.append(category_name)
        return collections
    
    def map_variants(self, salla_product: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Map Salla.sa variants to Shopify variants."""
        variants = []
        
        # Check if product has variants
        salla_variants = salla_product.get("variants", [])
        
        if salla_variants:
            # Product has variants
            for i, variant in enumerate(salla_variants):
                shopify_variant = {
                    "title": variant.get("name", f"Variant {i+1}"),
                    "price": str(variant.get("price", salla_product.get("price", "0"))),
                    "sku": variant.get("sku", ""),
                    "inventory_quantity": int(variant.get("quantity", 0)),
                    "inventory_management": "shopify",
                    "inventory_policy": "deny",
                    "fulfillment_service": "manual",
                    "requires_shipping": True,
                    "taxable": True,
                    "weight": float(variant.get("weight", 0)),
                    "weight_unit": "kg"
                }
                
                # Add compare at price if sale price exists
                if variant.get("sale_price") and variant.get("sale_price") != variant.get("price"):
                    shopify_variant["compare_at_price"] = str(variant.get("price"))
                    shopify_variant["price"] = str(variant.get("sale_price"))
                
                variants.append(shopify_variant)
        else:
            # Product has no variants, create default variant
            default_variant = {
                "title": "Default Title",
                "price": str(salla_product.get("price", "0")),
                "sku": salla_product.get("sku", ""),
                "inventory_quantity": int(salla_product.get("quantity", 0)),
                "inventory_management": "shopify",
                "inventory_policy": "deny",
                "fulfillment_service": "manual",
                "requires_shipping": True,
                "taxable": True,
                "weight": float(salla_product.get("weight", 0)),
                "weight_unit": "kg"
            }
            
            # Add compare at price if sale price exists
            if salla_product.get("sale_price") and salla_product.get("sale_price") != salla_product.get("price"):
                default_variant["compare_at_price"] = str(salla_product.get("price"))
                default_variant["price"] = str(salla_product.get("sale_price"))
            
            variants.append(default_variant)
        
        return variants
    
    def map_images(self, salla_images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Map Salla.sa images to Shopify format."""
        shopify_images = []
        
        for image in salla_images:
            shopify_image = {
                "src": image.get("url", ""),
                "alt": image.get("alt", "")
            }
            shopify_images.append(shopify_image)
        
        return shopify_images
    
    def map_seo(self, salla_product: Dict[str, Any]) -> Dict[str, str]:
        """Map SEO data."""
        seo_data = salla_product.get("seo", {})
        return {
            "seo_title": seo_data.get("title", salla_product.get("name", "")),
            "seo_description": seo_data.get("description", "")
        }
    
    def map_product(self, salla_product: Dict[str, Any]) -> Dict[str, Any]:
        """Map complete Salla.sa product to Shopify format."""
        try:
            # Get basic product info
            title = salla_product.get("name", "Untitled Product")
            description = salla_product.get("description", "")
            
            # Map vendor (brand)
            brand_id = salla_product.get("brand_id")
            vendor = self.brand_mapping.get(brand_id, "Unknown Brand") if brand_id else "Unknown Brand"
            
            # Map categories
            categories = salla_product.get("categories", [])
            product_type = self.map_product_type(categories)
            collections = self.map_collections(categories)
            
            # Create Shopify product
            shopify_product = {
                "title": title,
                "body_html": description,
                "vendor": vendor,
                "product_type": product_type,
                "handle": self.generate_handle(title),
                "status": self.map_status(salla_product.get("status", "active")),
                "tags": ", ".join(collections),  # Add collections as tags
                "variants": self.map_variants(salla_product),
                "images": self.map_images(salla_product.get("images", [])),
                **self.map_seo(salla_product)
            }
            
            # Add metadata
            shopify_product["metafields"] = [
                {
                    "namespace": "salla_migration",
                    "key": "original_id",
                    "value": str(salla_product.get("id", "")),
                    "type": "single_line_text_field"
                },
                {
                    "namespace": "salla_migration", 
                    "key": "migration_date",
                    "value": config.migration.test_mode,
                    "type": "single_line_text_field"
                }
            ]
            
            logger.debug(f"Mapped product: {title}")
            return {"product": shopify_product}
            
        except Exception as e:
            logger.error(f"Error mapping product {salla_product.get('id', 'unknown')}: {str(e)}")
            raise
    
    def map_products(self, salla_products: List[Dict[str, Any]], 
                    categories: List[Dict[str, Any]], 
                    brands: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Map multiple products."""
        logger.info(f"Mapping {len(salla_products)} products to Shopify format...")
        
        # Set up mappings
        self.set_category_mapping(categories)
        self.set_brand_mapping(brands)
        
        mapped_products = []
        for product in salla_products:
            try:
                mapped_product = self.map_product(product)
                mapped_products.append(mapped_product)
            except Exception as e:
                logger.error(f"Failed to map product {product.get('id', 'unknown')}: {str(e)}")
                continue
        
        logger.info(f"Successfully mapped {len(mapped_products)} products")
        return mapped_products
