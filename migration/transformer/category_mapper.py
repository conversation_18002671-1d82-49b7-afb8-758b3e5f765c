"""Category and collection mapping utilities."""

from typing import Dict, List, Any
from ..utils.logger import get_logger

logger = get_logger("category_mapper")

class CategoryMapper:
    """Maps Salla.sa categories to Shopify collections."""
    
    def __init__(self):
        self.category_mapping = {}
        self.collection_mapping = {}
    
    def create_collection_data(self, category: Dict[str, Any]) -> Dict[str, Any]:
        """Create Shopify collection data from Salla.sa category."""
        handle = self._generate_collection_handle(category["name"])
        
        return {
            "collection": {
                "title": category["name"],
                "handle": handle,
                "body_html": category.get("description", ""),
                "published": True,
                "sort_order": "best-selling",
                "template_suffix": "",
                "metafields": [
                    {
                        "namespace": "salla_migration",
                        "key": "original_category_id",
                        "value": str(category["id"]),
                        "type": "single_line_text_field"
                    }
                ]
            }
        }
    
    def _generate_collection_handle(self, name: str) -> str:
        """Generate collection handle from category name."""
        import re
        handle = re.sub(r'[^\w\s-]', '', name.lower())
        handle = re.sub(r'[-\s]+', '-', handle)
        return handle.strip('-')
    
    def map_categories_to_collections(self, categories: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Map all categories to collection format."""
        collections = []
        
        for category in categories:
            try:
                collection_data = self.create_collection_data(category)
                collections.append(collection_data)
                
                # Store mapping for later use
                self.category_mapping[category["id"]] = {
                    "name": category["name"],
                    "handle": collection_data["collection"]["handle"]
                }
                
                logger.debug(f"Mapped category '{category['name']}' to collection")
                
            except Exception as e:
                logger.error(f"Failed to map category {category.get('id', 'unknown')}: {str(e)}")
        
        logger.info(f"Mapped {len(collections)} categories to collections")
        return collections
    
    def get_collection_handle_by_category_id(self, category_id: str) -> str:
        """Get collection handle by category ID."""
        category_info = self.category_mapping.get(category_id)
        return category_info["handle"] if category_info else "general"
