"""Salla.sa data exporter."""

import asyncio
from datetime import datetime
from typing import Dict, List, Any
from tqdm.asyncio import tqdm
from ..config import config
from ..utils.logger import get_logger
from ..utils.file_utils import save_json
from ..utils.image_utils import download_product_images
from .client import SallaClient

logger = get_logger("salla_exporter")

class SallaExporter:
    """Export data from Salla.sa platform."""
    
    def __init__(self):
        self.client = SallaClient()
        self.export_data = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "source": "salla.sa",
                "version": "1.0"
            },
            "products": [],
            "categories": [],
            "brands": []
        }
    
    async def export_products(self, download_images: bool = True) -> List[Dict[str, Any]]:
        """Export all products from Salla.sa."""
        logger.info("Starting product export from Salla.sa...")
        
        # Get all products
        products = await self.client.get_all_products()
        logger.info(f"Found {len(products)} products to export")
        
        # Enrich products with detailed information
        enriched_products = []
        
        for product in tqdm(products, desc="Enriching products"):
            try:
                # Get detailed product info
                detailed_product = await self.client.get_product(product["id"])
                enriched_product = {**product, **detailed_product.get("data", {})}
                
                # Get product images
                try:
                    images_response = await self.client.get_product_images(product["id"])
                    if images_response.get("data"):
                        enriched_product["images"] = images_response["data"]
                        
                        # Download images if requested
                        if download_images and config.migration.enable_image_migration:
                            image_urls = [img.get("url") for img in images_response["data"] if img.get("url")]
                            if image_urls:
                                downloaded_images = await download_product_images(
                                    product["id"], 
                                    image_urls, 
                                    config.paths.images_dir
                                )
                                enriched_product["downloaded_images"] = downloaded_images
                
                except Exception as e:
                    logger.warning(f"Failed to get images for product {product['id']}: {str(e)}")
                
                enriched_products.append(enriched_product)
                
            except Exception as e:
                logger.error(f"Failed to enrich product {product['id']}: {str(e)}")
                # Add original product data even if enrichment fails
                enriched_products.append(product)
        
        self.export_data["products"] = enriched_products
        self.export_data["metadata"]["total_products"] = len(enriched_products)
        
        logger.info(f"Successfully exported {len(enriched_products)} products")
        return enriched_products
    
    async def export_categories(self) -> List[Dict[str, Any]]:
        """Export all categories from Salla.sa."""
        logger.info("Exporting categories from Salla.sa...")
        
        categories = await self.client.get_all_categories()
        self.export_data["categories"] = categories
        self.export_data["metadata"]["total_categories"] = len(categories)
        
        logger.info(f"Successfully exported {len(categories)} categories")
        return categories
    
    async def export_brands(self) -> List[Dict[str, Any]]:
        """Export all brands from Salla.sa."""
        logger.info("Exporting brands from Salla.sa...")
        
        brands = await self.client.get_all_brands()
        self.export_data["brands"] = brands
        self.export_data["metadata"]["total_brands"] = len(brands)
        
        logger.info(f"Successfully exported {len(brands)} brands")
        return brands
    
    async def export_all(self, download_images: bool = True) -> Dict[str, Any]:
        """Export all data from Salla.sa."""
        logger.info("Starting complete Salla.sa data export...")
        
        try:
            # Export all data types
            await asyncio.gather(
                self.export_products(download_images),
                self.export_categories(),
                self.export_brands()
            )
            
            # Save export data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = config.paths.export_dir / f"salla_export_{timestamp}.json"
            save_json(self.export_data, export_file)
            
            # Also save as latest
            latest_file = config.paths.export_dir / "salla_export_latest.json"
            save_json(self.export_data, latest_file)
            
            # Generate summary
            summary = self._generate_summary()
            summary_file = config.paths.export_dir / f"export_summary_{timestamp}.json"
            save_json(summary, summary_file)
            
            logger.info(f"Export completed successfully!")
            logger.info(f"Export file: {export_file}")
            logger.info(f"Summary: {summary}")
            
            return {
                "export_file": str(export_file),
                "summary": summary,
                "data": self.export_data
            }
            
        except Exception as e:
            logger.error(f"Export failed: {str(e)}")
            raise
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate export summary."""
        products = self.export_data.get("products", [])
        categories = self.export_data.get("categories", [])
        brands = self.export_data.get("brands", [])
        
        # Analyze products
        products_with_images = sum(1 for p in products if p.get("images"))
        products_with_variants = sum(1 for p in products if p.get("variants"))
        
        return {
            "export_date": self.export_data["metadata"]["export_date"],
            "totals": {
                "products": len(products),
                "categories": len(categories),
                "brands": len(brands)
            },
            "product_analysis": {
                "with_images": products_with_images,
                "with_variants": products_with_variants,
                "image_percentage": round((products_with_images / len(products)) * 100, 1) if products else 0,
                "variant_percentage": round((products_with_variants / len(products)) * 100, 1) if products else 0
            },
            "sample_products": [
                {
                    "id": p.get("id"),
                    "name": p.get("name"),
                    "price": p.get("price"),
                    "has_images": bool(p.get("images")),
                    "has_variants": bool(p.get("variants"))
                }
                for p in products[:5]
            ]
        }
