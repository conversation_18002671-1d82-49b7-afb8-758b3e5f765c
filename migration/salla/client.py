"""Salla.sa API client."""

import httpx
import asyncio
from typing import Dict, List, Optional, Any
from tenacity import retry, stop_after_attempt, wait_exponential
from ..config import config
from ..utils.logger import get_logger
from ..utils.rate_limiter import RateLimiter

logger = get_logger("salla_client")

class SallaClient:
    """Client for Salla.sa API."""
    
    def __init__(self):
        self.base_url = config.salla.base_url
        self.access_token = config.salla.access_token
        self.rate_limiter = RateLimiter(config.salla.rate_limit_per_second)
        
        self.headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, 
                           data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to Salla API with retry logic."""
        await self.rate_limiter.acquire()
        
        url = f"{self.base_url}{endpoint}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                logger.debug(f"Making request: {method} {url}")
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    params=params,
                    json=data
                )
                
                response.raise_for_status()
                result = response.json()
                
                logger.debug(f"Request successful: {method} {url}")
                return result
                
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                raise
            except Exception as e:
                logger.error(f"Request failed: {method} {url} - {str(e)}")
                raise
    
    async def get_products(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """Get products from Salla.sa."""
        logger.info(f"Fetching products page {page}")
        return await self._make_request("GET", "/products", params={
            "page": page,
            "per_page": per_page
        })
    
    async def get_product(self, product_id: str) -> Dict[str, Any]:
        """Get specific product details."""
        logger.info(f"Fetching product {product_id}")
        return await self._make_request("GET", f"/products/{product_id}")
    
    async def get_product_images(self, product_id: str) -> Dict[str, Any]:
        """Get product images."""
        logger.info(f"Fetching images for product {product_id}")
        return await self._make_request("GET", f"/products/{product_id}/images")
    
    async def get_categories(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """Get categories from Salla.sa."""
        logger.info(f"Fetching categories page {page}")
        return await self._make_request("GET", "/categories", params={
            "page": page,
            "per_page": per_page
        })
    
    async def get_brands(self, page: int = 1, per_page: int = 50) -> Dict[str, Any]:
        """Get brands from Salla.sa."""
        logger.info(f"Fetching brands page {page}")
        return await self._make_request("GET", "/brands", params={
            "page": page,
            "per_page": per_page
        })
    
    async def get_all_pages(self, fetch_function, *args, **kwargs) -> List[Dict[str, Any]]:
        """Fetch all pages of data from a paginated endpoint."""
        all_data = []
        page = 1
        
        while True:
            try:
                response = await fetch_function(*args, page=page, **kwargs)
                
                if not response.get("data"):
                    break
                
                data = response["data"]
                if not isinstance(data, list) or len(data) == 0:
                    break
                
                all_data.extend(data)
                
                # Check pagination info
                pagination = response.get("pagination", {})
                current_page = pagination.get("current_page", page)
                last_page = pagination.get("last_page", page)
                
                if current_page >= last_page:
                    break
                
                page += 1
                
            except Exception as e:
                logger.error(f"Error fetching page {page}: {str(e)}")
                break
        
        logger.info(f"Fetched total of {len(all_data)} items")
        return all_data
    
    async def get_all_products(self) -> List[Dict[str, Any]]:
        """Get all products from Salla.sa."""
        return await self.get_all_pages(self.get_products, per_page=50)
    
    async def get_all_categories(self) -> List[Dict[str, Any]]:
        """Get all categories from Salla.sa."""
        return await self.get_all_pages(self.get_categories, per_page=50)
    
    async def get_all_brands(self) -> List[Dict[str, Any]]:
        """Get all brands from Salla.sa."""
        return await self.get_all_pages(self.get_brands, per_page=50)
