"""Web scraper for Salla.sa stores."""

import asyncio
import re
import json
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import aiohttp
from fake_useragent import UserAgent
from ..config import config
from ..utils.logger import get_logger
from ..utils.rate_limiter import RateLimiter

logger = get_logger("salla_scraper")

class SallaScraper:
    """Web scraper for Salla.sa stores."""
    
    def __init__(self, store_url: str):
        self.store_url = store_url.rstrip('/')
        self.base_domain = urlparse(store_url).netloc
        self.session = None
        self.ua = UserAgent()
        self.rate_limiter = RateLimiter(1.0)  # 1 request per second for scraping
        
        # Common headers to mimic real browser
        self.headers = {
            'User-Agent': self.ua.random,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(limit=10, limit_per_host=5)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            headers=self.headers,
            connector=connector,
            timeout=timeout
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def fetch_page(self, url: str, retries: int = 3) -> Optional[str]:
        """Fetch a single page with retry logic."""
        await self.rate_limiter.acquire()
        
        for attempt in range(retries):
            try:
                logger.debug(f"Fetching page: {url} (attempt {attempt + 1})")
                
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        logger.debug(f"Successfully fetched: {url}")
                        return content
                    elif response.status == 429:
                        # Rate limited, wait longer
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time}s before retry")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        
            except Exception as e:
                logger.error(f"Error fetching {url}: {str(e)}")
                if attempt < retries - 1:
                    await asyncio.sleep(1)
        
        logger.error(f"Failed to fetch {url} after {retries} attempts")
        return None
    
    async def discover_product_urls(self) -> List[str]:
        """Discover all product URLs from the store."""
        logger.info("Discovering product URLs...")
        product_urls = set()
        
        # Try different common paths for product listings
        listing_paths = [
            '/products',
            '/shop',
            '/store',
            '/catalog',
            '/collections/all',
            '/'
        ]
        
        for path in listing_paths:
            url = urljoin(self.store_url, path)
            content = await self.fetch_page(url)
            
            if content:
                urls = self.extract_product_urls_from_page(content)
                product_urls.update(urls)
                logger.info(f"Found {len(urls)} product URLs from {path}")
        
        # Try to find pagination and get more pages
        await self.discover_paginated_products(product_urls)
        
        logger.info(f"Total discovered product URLs: {len(product_urls)}")
        return list(product_urls)
    
    def extract_product_urls_from_page(self, content: str) -> List[str]:
        """Extract product URLs from a page content."""
        soup = BeautifulSoup(content, 'html.parser')
        product_urls = []
        
        # Common selectors for product links
        selectors = [
            'a[href*="/product/"]',
            'a[href*="/products/"]',
            'a[href*="/p/"]',
            '.product-item a',
            '.product-card a',
            '.product-link',
            '[data-product-url]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href') or link.get('data-product-url')
                if href:
                    full_url = urljoin(self.store_url, href)
                    if self.is_product_url(full_url):
                        product_urls.append(full_url)
        
        return list(set(product_urls))  # Remove duplicates
    
    def is_product_url(self, url: str) -> bool:
        """Check if URL looks like a product URL."""
        product_patterns = [
            r'/product/',
            r'/products/',
            r'/p/',
            r'/item/',
        ]
        
        return any(re.search(pattern, url, re.IGNORECASE) for pattern in product_patterns)
    
    async def discover_paginated_products(self, product_urls: set) -> None:
        """Discover products from paginated listings."""
        logger.info("Checking for paginated product listings...")
        
        # Try common pagination patterns
        for page in range(2, 11):  # Check up to page 10
            pagination_urls = [
                f"{self.store_url}/products?page={page}",
                f"{self.store_url}/shop?page={page}",
                f"{self.store_url}/?page={page}",
            ]
            
            found_new_products = False
            for url in pagination_urls:
                content = await self.fetch_page(url)
                if content:
                    new_urls = self.extract_product_urls_from_page(content)
                    if new_urls:
                        before_count = len(product_urls)
                        product_urls.update(new_urls)
                        after_count = len(product_urls)
                        
                        if after_count > before_count:
                            found_new_products = True
                            logger.info(f"Found {after_count - before_count} new products on page {page}")
            
            if not found_new_products:
                logger.info(f"No new products found on page {page}, stopping pagination")
                break
    
    async def scrape_product(self, product_url: str) -> Optional[Dict[str, Any]]:
        """Scrape a single product page."""
        logger.debug(f"Scraping product: {product_url}")
        
        content = await self.fetch_page(product_url)
        if not content:
            return None
        
        soup = BeautifulSoup(content, 'html.parser')
        
        try:
            product_data = {
                'url': product_url,
                'id': self.extract_product_id(product_url, soup),
                'name': self.extract_product_name(soup),
                'description': self.extract_product_description(soup),
                'price': self.extract_product_price(soup),
                'sale_price': self.extract_sale_price(soup),
                'sku': self.extract_product_sku(soup),
                'images': self.extract_product_images(soup),
                'variants': self.extract_product_variants(soup),
                'categories': self.extract_product_categories(soup),
                'brand': self.extract_product_brand(soup),
                'availability': self.extract_availability(soup),
                'seo': self.extract_seo_data(soup),
                'structured_data': self.extract_structured_data(content)
            }
            
            logger.debug(f"Successfully scraped product: {product_data.get('name', 'Unknown')}")
            return product_data
            
        except Exception as e:
            logger.error(f"Error scraping product {product_url}: {str(e)}")
            return None
    
    def extract_product_id(self, url: str, soup: BeautifulSoup) -> str:
        """Extract product ID from URL or page."""
        # Try to extract from URL
        id_match = re.search(r'/(?:product|p)/([^/?]+)', url)
        if id_match:
            return id_match.group(1)
        
        # Try to extract from page data
        selectors = [
            '[data-product-id]',
            '[data-id]',
            '#product-id'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get('data-product-id') or element.get('data-id') or element.get('value')
        
        # Fallback: use URL as ID
        return url.split('/')[-1].split('?')[0]
    
    def extract_product_name(self, soup: BeautifulSoup) -> str:
        """Extract product name."""
        selectors = [
            'h1.product-title',
            'h1.product-name',
            '.product-title h1',
            '.product-name h1',
            'h1',
            '[data-product-title]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        # Fallback to page title
        title = soup.find('title')
        return title.get_text(strip=True) if title else "Unknown Product"
    
    def extract_product_description(self, soup: BeautifulSoup) -> str:
        """Extract product description."""
        selectors = [
            '.product-description',
            '.product-details',
            '.product-content',
            '[data-product-description]',
            '.description'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get_text(strip=True)
        
        return ""
    
    def extract_product_price(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract product price."""
        selectors = [
            '.product-price .price',
            '.price',
            '.product-price',
            '[data-price]',
            '.current-price'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                price_text = element.get('data-price') or element.get_text(strip=True)
                price = self.parse_price(price_text)
                if price:
                    return price
        
        return None
    
    def extract_sale_price(self, soup: BeautifulSoup) -> Optional[float]:
        """Extract sale/discounted price."""
        selectors = [
            '.sale-price',
            '.discounted-price',
            '.offer-price',
            '[data-sale-price]'
        ]
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                price_text = element.get('data-sale-price') or element.get_text(strip=True)
                price = self.parse_price(price_text)
                if price:
                    return price
        
        return None
    
    def parse_price(self, price_text: str) -> Optional[float]:
        """Parse price from text."""
        if not price_text:
            return None
        
        # Remove currency symbols and extract numbers
        price_match = re.search(r'[\d,]+\.?\d*', price_text.replace(',', ''))
        if price_match:
            try:
                return float(price_match.group())
            except ValueError:
                pass
        
        return None

    def extract_product_sku(self, soup: BeautifulSoup) -> str:
        """Extract product SKU."""
        selectors = [
            '[data-sku]',
            '.product-sku',
            '.sku',
            '[data-product-sku]'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get('data-sku') or element.get_text(strip=True)

        return ""

    def extract_product_images(self, soup: BeautifulSoup) -> List[Dict[str, str]]:
        """Extract product images."""
        images = []

        # Try different image selectors
        selectors = [
            '.product-images img',
            '.product-gallery img',
            '.product-image img',
            '[data-product-image]',
            '.gallery img'
        ]

        for selector in selectors:
            img_elements = soup.select(selector)
            for img in img_elements:
                src = img.get('src') or img.get('data-src') or img.get('data-original')
                if src:
                    # Convert relative URLs to absolute
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(self.store_url, src)

                    images.append({
                        'url': src,
                        'alt': img.get('alt', '')
                    })

        return images

    def extract_product_variants(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Extract product variants."""
        variants = []

        # Look for variant selectors (size, color, etc.)
        variant_selectors = [
            '.product-variants select option',
            '.variant-options input',
            '[data-variant]'
        ]

        for selector in variant_selectors:
            elements = soup.select(selector)
            for element in elements:
                variant_name = element.get_text(strip=True) or element.get('value')
                variant_price = element.get('data-price')

                if variant_name and variant_name.lower() not in ['choose', 'select', '']:
                    variants.append({
                        'name': variant_name,
                        'price': self.parse_price(variant_price) if variant_price else None,
                        'sku': element.get('data-sku', ''),
                        'availability': element.get('data-available', True)
                    })

        return variants

    def extract_product_categories(self, soup: BeautifulSoup) -> List[str]:
        """Extract product categories."""
        categories = []

        # Try breadcrumb navigation
        breadcrumb_selectors = [
            '.breadcrumb a',
            '.breadcrumbs a',
            '[data-breadcrumb] a'
        ]

        for selector in breadcrumb_selectors:
            links = soup.select(selector)
            for link in links:
                category = link.get_text(strip=True)
                if category and category.lower() not in ['home', 'الرئيسية']:
                    categories.append(category)

        # Try category tags
        category_selectors = [
            '.product-categories a',
            '.product-tags a',
            '[data-category]'
        ]

        for selector in category_selectors:
            elements = soup.select(selector)
            for element in elements:
                category = element.get_text(strip=True) or element.get('data-category')
                if category:
                    categories.append(category)

        return list(set(categories))  # Remove duplicates

    def extract_product_brand(self, soup: BeautifulSoup) -> str:
        """Extract product brand."""
        selectors = [
            '.product-brand',
            '.brand',
            '[data-brand]',
            '.manufacturer'
        ]

        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                return element.get('data-brand') or element.get_text(strip=True)

        return ""

    def extract_availability(self, soup: BeautifulSoup) -> bool:
        """Extract product availability."""
        # Look for out of stock indicators
        out_of_stock_selectors = [
            '.out-of-stock',
            '.sold-out',
            '[data-available="false"]'
        ]

        for selector in out_of_stock_selectors:
            if soup.select_one(selector):
                return False

        # Look for in stock indicators
        in_stock_selectors = [
            '.in-stock',
            '.available',
            '[data-available="true"]'
        ]

        for selector in in_stock_selectors:
            if soup.select_one(selector):
                return True

        return True  # Default to available

    def extract_seo_data(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract SEO metadata."""
        seo_data = {}

        # Meta title
        title = soup.find('title')
        if title:
            seo_data['title'] = title.get_text(strip=True)

        # Meta description
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            seo_data['description'] = meta_desc.get('content', '')

        # Open Graph data
        og_title = soup.find('meta', attrs={'property': 'og:title'})
        if og_title:
            seo_data['og_title'] = og_title.get('content', '')

        og_desc = soup.find('meta', attrs={'property': 'og:description'})
        if og_desc:
            seo_data['og_description'] = og_desc.get('content', '')

        return seo_data

    def extract_structured_data(self, content: str) -> Dict[str, Any]:
        """Extract structured data (JSON-LD)."""
        structured_data = {}

        # Look for JSON-LD structured data
        json_ld_pattern = r'<script[^>]*type=["\']application/ld\+json["\'][^>]*>(.*?)</script>'
        matches = re.findall(json_ld_pattern, content, re.DOTALL | re.IGNORECASE)

        for match in matches:
            try:
                data = json.loads(match.strip())
                if isinstance(data, dict) and data.get('@type') == 'Product':
                    structured_data = data
                    break
            except json.JSONDecodeError:
                continue

        return structured_data

    async def scrape_all_products(self, max_products: Optional[int] = None) -> List[Dict[str, Any]]:
        """Scrape all products from the store."""
        logger.info(f"Starting to scrape products from {self.store_url}")

        # Discover product URLs
        product_urls = await self.discover_product_urls()

        if max_products:
            product_urls = product_urls[:max_products]
            logger.info(f"Limited to {max_products} products")

        # Scrape each product
        products = []
        failed_count = 0

        for i, url in enumerate(product_urls, 1):
            logger.info(f"Scraping product {i}/{len(product_urls)}: {url}")

            product_data = await self.scrape_product(url)
            if product_data:
                products.append(product_data)
            else:
                failed_count += 1

            # Add delay between products to be respectful
            await asyncio.sleep(1)

        logger.info(f"Scraping completed: {len(products)} successful, {failed_count} failed")
        return products
