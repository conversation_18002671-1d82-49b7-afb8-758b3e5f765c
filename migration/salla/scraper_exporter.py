"""Web scraping-based exporter for Salla.sa stores."""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional
from tqdm.asyncio import tqdm
from ..config import config
from ..utils.logger import get_logger
from ..utils.file_utils import save_json
from .scraper import SallaScraper

logger = get_logger("salla_scraper_exporter")

class SallaScraperExporter:
    """Export data from Salla.sa using web scraping."""
    
    def __init__(self, store_url: str):
        self.store_url = store_url
        self.export_data = {
            "metadata": {
                "export_date": datetime.now().isoformat(),
                "source": "salla.sa_scraping",
                "store_url": store_url,
                "version": "1.0"
            },
            "products": [],
            "categories": set(),  # Will be converted to list later
            "brands": set()       # Will be converted to list later
        }
    
    async def export_products(self, max_products: Optional[int] = None, 
                            download_images: bool = True) -> List[Dict[str, Any]]:
        """Export all products using web scraping."""
        logger.info(f"Starting product scraping from {self.store_url}")
        
        async with SallaScraper(self.store_url) as scraper:
            # Scrape all products
            scraped_products = await scraper.scrape_all_products(max_products)
            
            # Process scraped data
            processed_products = []
            
            for product in tqdm(scraped_products, desc="Processing scraped products"):
                try:
                    # Convert scraped data to standard format
                    processed_product = self._convert_scraped_product(product)
                    processed_products.append(processed_product)
                    
                    # Collect categories and brands
                    if product.get('categories'):
                        self.export_data['categories'].update(product['categories'])
                    
                    if product.get('brand'):
                        self.export_data['brands'].add(product['brand'])
                    
                    # Download images if requested
                    if download_images and config.migration.enable_image_migration:
                        await self._download_product_images(processed_product)
                
                except Exception as e:
                    logger.error(f"Failed to process scraped product {product.get('url', 'unknown')}: {str(e)}")
                    continue
            
            self.export_data["products"] = processed_products
            
            # Convert sets to lists for JSON serialization
            self.export_data["categories"] = [
                {"id": i, "name": cat} for i, cat in enumerate(self.export_data["categories"], 1)
            ]
            self.export_data["brands"] = [
                {"id": i, "name": brand} for i, brand in enumerate(self.export_data["brands"], 1)
            ]
            
            self.export_data["metadata"]["total_products"] = len(processed_products)
            self.export_data["metadata"]["total_categories"] = len(self.export_data["categories"])
            self.export_data["metadata"]["total_brands"] = len(self.export_data["brands"])
            
            logger.info(f"Successfully scraped {len(processed_products)} products")
            return processed_products
    
    def _convert_scraped_product(self, scraped_product: Dict[str, Any]) -> Dict[str, Any]:
        """Convert scraped product data to standard format."""
        # Map scraped data to standard product format
        product = {
            "id": scraped_product.get("id", ""),
            "name": scraped_product.get("name", ""),
            "description": scraped_product.get("description", ""),
            "price": scraped_product.get("price", 0),
            "sale_price": scraped_product.get("sale_price"),
            "sku": scraped_product.get("sku", ""),
            "quantity": 1 if scraped_product.get("availability", True) else 0,
            "status": "active" if scraped_product.get("availability", True) else "inactive",
            "images": scraped_product.get("images", []),
            "variants": scraped_product.get("variants", []),
            "categories": scraped_product.get("categories", []),
            "brand_id": scraped_product.get("brand", ""),
            "seo": scraped_product.get("seo", {}),
            "url": scraped_product.get("url", ""),
            "structured_data": scraped_product.get("structured_data", {})
        }
        
        # If no variants, create a default one
        if not product["variants"]:
            product["variants"] = [{
                "id": f"{product['id']}_default",
                "name": "Default",
                "price": product["price"],
                "sale_price": product["sale_price"],
                "sku": product["sku"],
                "quantity": product["quantity"]
            }]
        
        return product
    
    async def _download_product_images(self, product: Dict[str, Any]) -> None:
        """Download images for a product."""
        try:
            from ..utils.image_utils import download_product_images
            
            image_urls = [img.get("url") for img in product.get("images", []) if img.get("url")]
            if image_urls:
                downloaded_images = await download_product_images(
                    product["id"], 
                    image_urls, 
                    config.paths.images_dir
                )
                product["downloaded_images"] = downloaded_images
                
        except Exception as e:
            logger.error(f"Failed to download images for product {product['id']}: {str(e)}")
    
    async def export_all(self, max_products: Optional[int] = None, 
                        download_images: bool = True) -> Dict[str, Any]:
        """Export all data using web scraping."""
        logger.info("Starting complete Salla.sa data scraping...")
        
        try:
            # Export products (categories and brands are extracted during product processing)
            await self.export_products(max_products, download_images)
            
            # Save export data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            export_file = config.paths.export_dir / f"salla_scrape_export_{timestamp}.json"
            save_json(self.export_data, export_file)
            
            # Also save as latest
            latest_file = config.paths.export_dir / "salla_scrape_export_latest.json"
            save_json(self.export_data, latest_file)
            
            # Generate summary
            summary = self._generate_summary()
            summary_file = config.paths.export_dir / f"scrape_export_summary_{timestamp}.json"
            save_json(summary, summary_file)
            
            logger.info(f"Scraping export completed successfully!")
            logger.info(f"Export file: {export_file}")
            logger.info(f"Summary: {summary}")
            
            return {
                "export_file": str(export_file),
                "summary": summary,
                "data": self.export_data
            }
            
        except Exception as e:
            logger.error(f"Scraping export failed: {str(e)}")
            raise
    
    def _generate_summary(self) -> Dict[str, Any]:
        """Generate export summary."""
        products = self.export_data.get("products", [])
        categories = self.export_data.get("categories", [])
        brands = self.export_data.get("brands", [])
        
        # Analyze products
        products_with_images = sum(1 for p in products if p.get("images"))
        products_with_variants = sum(1 for p in products if len(p.get("variants", [])) > 1)
        
        return {
            "export_date": self.export_data["metadata"]["export_date"],
            "store_url": self.store_url,
            "method": "web_scraping",
            "totals": {
                "products": len(products),
                "categories": len(categories),
                "brands": len(brands)
            },
            "product_analysis": {
                "with_images": products_with_images,
                "with_variants": products_with_variants,
                "image_percentage": round((products_with_images / len(products)) * 100, 1) if products else 0,
                "variant_percentage": round((products_with_variants / len(products)) * 100, 1) if products else 0
            },
            "sample_products": [
                {
                    "id": p.get("id"),
                    "name": p.get("name"),
                    "price": p.get("price"),
                    "url": p.get("url"),
                    "has_images": bool(p.get("images")),
                    "has_variants": len(p.get("variants", [])) > 1
                }
                for p in products[:5]
            ]
        }
