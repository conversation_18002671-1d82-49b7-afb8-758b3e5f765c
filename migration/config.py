"""Configuration management for the migration tool."""

import os
from pathlib import Path
from typing import Optional
from dotenv import load_dotenv
from pydantic import BaseSettings, validator

# Load environment variables
load_dotenv()

class SallaConfig(BaseSettings):
    """Salla.sa API configuration."""
    base_url: str = "https://api.salla.dev/admin/v2"
    client_id: Optional[str] = None
    client_secret: Optional[str] = None
    access_token: Optional[str] = None
    store_url: Optional[str] = None
    rate_limit_per_hour: int = 1000
    rate_limit_per_second: int = 1

    class Config:
        env_prefix = "SALLA_"

class ShopifyConfig(BaseSettings):
    """Shopify API configuration."""
    shop_name: Optional[str] = None
    access_token: Optional[str] = None
    api_version: str = "2023-10"
    rate_limit_per_second: int = 2
    bulk_operations_limit: int = 1000

    @validator('base_url', pre=True, always=True)
    def set_base_url(cls, v, values):
        shop_name = values.get('shop_name')
        if shop_name:
            return f"https://{shop_name}.myshopify.com/admin/api/2023-10"
        return None

    class Config:
        env_prefix = "SHOPIFY_"

class MigrationConfig(BaseSettings):
    """Migration process configuration."""
    batch_size: int = 50
    max_retries: int = 3
    retry_delay: int = 1000  # milliseconds
    image_download_timeout: int = 30  # seconds
    enable_image_migration: bool = True
    enable_variant_migration: bool = True
    enable_category_migration: bool = True
    test_mode: bool = True

    class Config:
        env_prefix = "MIGRATION_"

class PathConfig:
    """File path configuration."""
    def __init__(self, base_dir: Optional[Path] = None):
        self.base_dir = base_dir or Path.cwd()
        self.data_dir = self.base_dir / "data"
        self.export_dir = self.data_dir / "exports"
        self.import_dir = self.data_dir / "imports"
        self.images_dir = self.data_dir / "images"
        self.backup_dir = self.data_dir / "backups"
        self.logs_dir = self.base_dir / "logs"
        
        # Ensure directories exist
        for dir_path in [self.data_dir, self.export_dir, self.import_dir, 
                        self.images_dir, self.backup_dir, self.logs_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

class LoggingConfig(BaseSettings):
    """Logging configuration."""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name} | {message}"
    rotation: str = "10 MB"
    retention: str = "1 week"
    
    class Config:
        env_prefix = "LOG_"

class Config:
    """Main configuration class."""
    
    def __init__(self, base_dir: Optional[Path] = None):
        self.salla = SallaConfig()
        self.shopify = ShopifyConfig()
        self.migration = MigrationConfig()
        self.paths = PathConfig(base_dir)
        self.logging = LoggingConfig()
        
        # Set test mode based on environment
        if os.getenv("NODE_ENV") == "production":
            self.migration.test_mode = False
    
    def validate(self) -> list[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        if not self.salla.access_token:
            errors.append("SALLA_ACCESS_TOKEN is required")
        
        if not self.shopify.shop_name:
            errors.append("SHOPIFY_SHOP_NAME is required")
            
        if not self.shopify.access_token:
            errors.append("SHOPIFY_ACCESS_TOKEN is required")
        
        return errors
    
    def is_valid(self) -> bool:
        """Check if configuration is valid."""
        return len(self.validate()) == 0

# Global configuration instance
config = Config()
