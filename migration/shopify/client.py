"""Shopify API client."""

import httpx
import asyncio
from typing import Dict, List, Optional, Any
from tenacity import retry, stop_after_attempt, wait_exponential
from ..config import config
from ..utils.logger import get_logger
from ..utils.rate_limiter import RateLimiter

logger = get_logger("shopify_client")

class ShopifyClient:
    """Client for Shopify Admin API."""
    
    def __init__(self):
        self.shop_name = config.shopify.shop_name
        self.access_token = config.shopify.access_token
        self.base_url = f"https://{self.shop_name}.myshopify.com/admin/api/{config.shopify.api_version}"
        self.rate_limiter = RateLimiter(config.shopify.rate_limit_per_second)
        
        self.headers = {
            "X-Shopify-Access-Token": self.access_token,
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None, 
                           data: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to Shopify API with retry logic."""
        await self.rate_limiter.acquire()
        
        url = f"{self.base_url}{endpoint}"
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            try:
                logger.debug(f"Making request: {method} {url}")
                response = await client.request(
                    method=method,
                    url=url,
                    headers=self.headers,
                    params=params,
                    json=data
                )
                
                # Log rate limit info
                if "X-Shopify-Shop-Api-Call-Limit" in response.headers:
                    limit_info = response.headers["X-Shopify-Shop-Api-Call-Limit"]
                    logger.debug(f"API Call Limit: {limit_info}")
                
                response.raise_for_status()
                result = response.json()
                
                logger.debug(f"Request successful: {method} {url}")
                return result
                
            except httpx.HTTPStatusError as e:
                logger.error(f"HTTP error {e.response.status_code}: {e.response.text}")
                raise
            except Exception as e:
                logger.error(f"Request failed: {method} {url} - {str(e)}")
                raise
    
    # Product methods
    async def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new product in Shopify."""
        logger.info(f"Creating product: {product_data.get('product', {}).get('title', 'Unknown')}")
        return await self._make_request("POST", "/products.json", data=product_data)
    
    async def update_product(self, product_id: str, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Update an existing product."""
        logger.info(f"Updating product: {product_id}")
        return await self._make_request("PUT", f"/products/{product_id}.json", data=product_data)
    
    async def get_products(self, limit: int = 50, since_id: Optional[str] = None) -> Dict[str, Any]:
        """Get products from Shopify."""
        params = {"limit": limit}
        if since_id:
            params["since_id"] = since_id
        
        logger.info(f"Fetching products (limit: {limit})")
        return await self._make_request("GET", "/products.json", params=params)
    
    async def get_product(self, product_id: str) -> Dict[str, Any]:
        """Get specific product."""
        logger.info(f"Fetching product {product_id}")
        return await self._make_request("GET", f"/products/{product_id}.json")
    
    async def delete_product(self, product_id: str) -> None:
        """Delete a product."""
        logger.info(f"Deleting product {product_id}")
        await self._make_request("DELETE", f"/products/{product_id}.json")
    
    # Image methods
    async def create_product_image(self, product_id: str, image_data: Dict[str, Any]) -> Dict[str, Any]:
        """Add image to product."""
        logger.info(f"Adding image to product {product_id}")
        return await self._make_request("POST", f"/products/{product_id}/images.json", 
                                      data={"image": image_data})
    
    # Collection methods
    async def get_collections(self, limit: int = 50) -> Dict[str, Any]:
        """Get collections from Shopify."""
        logger.info(f"Fetching collections (limit: {limit})")
        return await self._make_request("GET", "/collections.json", params={"limit": limit})
    
    async def create_collection(self, collection_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new collection."""
        logger.info(f"Creating collection: {collection_data.get('collection', {}).get('title', 'Unknown')}")
        return await self._make_request("POST", "/collections.json", data=collection_data)
    
    async def add_product_to_collection(self, collection_id: str, product_id: str) -> Dict[str, Any]:
        """Add product to collection."""
        logger.info(f"Adding product {product_id} to collection {collection_id}")
        collect_data = {
            "collect": {
                "product_id": product_id,
                "collection_id": collection_id
            }
        }
        return await self._make_request("POST", "/collects.json", data=collect_data)
    
    # Bulk operations
    async def create_bulk_operation(self, query: str) -> Dict[str, Any]:
        """Create a bulk operation."""
        logger.info("Creating bulk operation")
        mutation = f"""
        mutation {{
            bulkOperationRunQuery(
                query: """{query}"""
            ) {{
                bulkOperation {{
                    id
                    status
                }}
                userErrors {{
                    field
                    message
                }}
            }}
        }}
        """
        return await self._make_request("POST", "/graphql.json", data={"query": mutation})
    
    async def get_bulk_operation_status(self, operation_id: str) -> Dict[str, Any]:
        """Get bulk operation status."""
        logger.info(f"Checking bulk operation status: {operation_id}")
        query = f"""
        query {{
            node(id: "{operation_id}") {{
                ... on BulkOperation {{
                    id
                    status
                    errorCode
                    createdAt
                    completedAt
                    objectCount
                    fileSize
                    url
                    partialDataUrl
                }}
            }}
        }}
        """
        return await self._make_request("POST", "/graphql.json", data={"query": query})
    
    # Utility methods
    async def get_all_products(self) -> List[Dict[str, Any]]:
        """Get all products from Shopify."""
        all_products = []
        since_id = None
        
        while True:
            try:
                response = await self.get_products(limit=250, since_id=since_id)
                products = response.get("products", [])
                
                if not products:
                    break
                
                all_products.extend(products)
                since_id = products[-1]["id"]
                
                if len(products) < 250:
                    break
                    
            except Exception as e:
                logger.error(f"Error fetching products: {str(e)}")
                break
        
        logger.info(f"Fetched total of {len(all_products)} products")
        return all_products
    
    async def test_connection(self) -> bool:
        """Test connection to Shopify API."""
        try:
            await self.get_products(limit=1)
            logger.info("Shopify API connection successful")
            return True
        except Exception as e:
            logger.error(f"Shopify API connection failed: {str(e)}")
            return False
