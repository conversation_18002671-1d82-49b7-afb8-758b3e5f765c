"""Shopify product importer."""

import asyncio
from typing import Dict, List, Any
from tqdm.asyncio import tqdm
from ..config import config
from ..utils.logger import get_logger
from ..utils.file_utils import save_json
from .client import ShopifyClient

logger = get_logger("shopify_importer")

class ShopifyImporter:
    """Import products to Shopify."""
    
    def __init__(self):
        self.client = ShopifyClient()
        self.import_results = {
            "successful": [],
            "failed": [],
            "summary": {
                "total": 0,
                "successful": 0,
                "failed": 0
            }
        }
    
    async def import_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """Import a single product to Shopify."""
        try:
            product_title = product_data["product"]["title"]
            logger.info(f"Importing product: {product_title}")
            
            # Create product
            result = await self.client.create_product(product_data)
            
            if result.get("product"):
                logger.info(f"Successfully imported: {product_title}")
                return {
                    "status": "success",
                    "product_id": result["product"]["id"],
                    "title": product_title,
                    "shopify_data": result["product"]
                }
            else:
                logger.error(f"Failed to import {product_title}: No product in response")
                return {
                    "status": "failed",
                    "title": product_title,
                    "error": "No product in response"
                }
                
        except Exception as e:
            logger.error(f"Failed to import {product_data['product']['title']}: {str(e)}")
            return {
                "status": "failed",
                "title": product_data["product"]["title"],
                "error": str(e)
            }
    
    async def import_products_batch(self, products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Import a batch of products."""
        tasks = [self.import_product(product) for product in products]
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def import_products(self, products: List[Dict[str, Any]], 
                            batch_size: int = None) -> Dict[str, Any]:
        """Import multiple products to Shopify."""
        if batch_size is None:
            batch_size = config.migration.batch_size
        
        logger.info(f"Starting import of {len(products)} products (batch size: {batch_size})")
        
        self.import_results["summary"]["total"] = len(products)
        
        # Process products in batches
        for i in tqdm(range(0, len(products), batch_size), desc="Importing batches"):
            batch = products[i:i + batch_size]
            batch_results = await self.import_products_batch(batch)
            
            # Process batch results
            for result in batch_results:
                if isinstance(result, Exception):
                    self.import_results["failed"].append({
                        "status": "failed",
                        "error": str(result)
                    })
                elif result["status"] == "success":
                    self.import_results["successful"].append(result)
                else:
                    self.import_results["failed"].append(result)
            
            # Add delay between batches to respect rate limits
            if i + batch_size < len(products):
                await asyncio.sleep(1)
        
        # Update summary
        self.import_results["summary"]["successful"] = len(self.import_results["successful"])
        self.import_results["summary"]["failed"] = len(self.import_results["failed"])
        
        # Save import results
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = config.paths.data_dir / f"import_results_{timestamp}.json"
        save_json(self.import_results, results_file)
        
        logger.info(f"Import completed: {self.import_results['summary']['successful']} successful, "
                   f"{self.import_results['summary']['failed']} failed")
        
        return self.import_results["summary"]
    
    async def create_collections_from_categories(self, categories: List[Dict[str, Any]]) -> Dict[str, str]:
        """Create Shopify collections from Salla.sa categories."""
        logger.info(f"Creating {len(categories)} collections...")
        
        collection_mapping = {}
        
        for category in categories:
            try:
                collection_data = {
                    "collection": {
                        "title": category["name"],
                        "handle": category["name"].lower().replace(" ", "-"),
                        "body_html": category.get("description", ""),
                        "published": True
                    }
                }
                
                result = await self.client.create_collection(collection_data)
                
                if result.get("collection"):
                    collection_mapping[category["id"]] = result["collection"]["id"]
                    logger.info(f"Created collection: {category['name']}")
                
            except Exception as e:
                logger.error(f"Failed to create collection {category['name']}: {str(e)}")
        
        return collection_mapping
    
    async def upload_product_images(self, product_id: str, image_paths: List[str]) -> List[Dict[str, Any]]:
        """Upload images for a product."""
        uploaded_images = []
        
        for image_path in image_paths:
            try:
                # For now, we'll use the image URLs from Salla.sa
                # In a full implementation, you'd upload the downloaded images
                image_data = {
                    "src": image_path,  # This should be the downloaded image URL or base64
                    "alt": f"Product image"
                }
                
                result = await self.client.create_product_image(product_id, image_data)
                
                if result.get("image"):
                    uploaded_images.append(result["image"])
                    logger.info(f"Uploaded image for product {product_id}")
                
            except Exception as e:
                logger.error(f"Failed to upload image for product {product_id}: {str(e)}")
        
        return uploaded_images
