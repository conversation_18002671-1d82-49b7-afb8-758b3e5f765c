"""Rate limiting utilities."""

import asyncio
import time
from typing import Optional
from asyncio_throttle import Throttler

class RateLimiter:
    """Rate limiter for API requests."""
    
    def __init__(self, requests_per_second: float, burst_size: Optional[int] = None):
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size or int(requests_per_second * 2)
        self.throttler = Throttler(rate_limit=requests_per_second, period=1.0)
        self.last_request_time = 0
        
    async def acquire(self):
        """Acquire permission to make a request."""
        async with self.throttler:
            # Additional delay to ensure we don't exceed rate limits
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            min_interval = 1.0 / self.requests_per_second
            
            if time_since_last < min_interval:
                await asyncio.sleep(min_interval - time_since_last)
            
            self.last_request_time = time.time()
    
    def acquire_sync(self):
        """Synchronous version of acquire."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        min_interval = 1.0 / self.requests_per_second
        
        if time_since_last < min_interval:
            time.sleep(min_interval - time_since_last)
        
        self.last_request_time = time.time()
