"""Image handling utilities."""

import aiohttp
import asyncio
from pathlib import Path
from typing import Op<PERSON>, Tuple
from PIL import Image
import hashlib
from ..utils.logger import get_logger

logger = get_logger("image_utils")

async def download_image(url: str, save_path: Path, timeout: int = 30) -> bool:
    """Download image from URL and save to file."""
    try:
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=timeout)) as session:
            async with session.get(url) as response:
                if response.status == 200:
                    content = await response.read()
                    
                    # Ensure directory exists
                    save_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Save image
                    with open(save_path, 'wb') as f:
                        f.write(content)
                    
                    logger.info(f"Downloaded image: {url} -> {save_path}")
                    return True
                else:
                    logger.error(f"Failed to download image {url}: HTTP {response.status}")
                    return False
                    
    except Exception as e:
        logger.error(f"Error downloading image {url}: {str(e)}")
        return False

def validate_image(file_path: Path) -> bool:
    """Validate that file is a valid image."""
    try:
        with Image.open(file_path) as img:
            img.verify()
        return True
    except Exception as e:
        logger.error(f"Invalid image {file_path}: {str(e)}")
        return False

def get_image_info(file_path: Path) -> Optional[dict]:
    """Get image information (size, format, etc.)."""
    try:
        with Image.open(file_path) as img:
            return {
                'format': img.format,
                'mode': img.mode,
                'size': img.size,
                'width': img.width,
                'height': img.height,
                'file_size': file_path.stat().st_size
            }
    except Exception as e:
        logger.error(f"Error getting image info for {file_path}: {str(e)}")
        return None

def generate_image_filename(url: str, product_id: str, index: int = 0) -> str:
    """Generate unique filename for image."""
    # Create hash of URL for uniqueness
    url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
    
    # Get file extension from URL
    extension = Path(url).suffix.lower()
    if not extension or extension not in ['.jpg', '.jpeg', '.png', '.gif', '.webp']:
        extension = '.jpg'  # Default extension
    
    return f"{product_id}_{index}_{url_hash}{extension}"

async def download_product_images(product_id: str, image_urls: list, images_dir: Path) -> list:
    """Download all images for a product."""
    downloaded_images = []
    
    for i, url in enumerate(image_urls):
        filename = generate_image_filename(url, product_id, i)
        save_path = images_dir / filename
        
        # Skip if already downloaded
        if save_path.exists() and validate_image(save_path):
            logger.info(f"Image already exists: {save_path}")
            downloaded_images.append({
                'original_url': url,
                'local_path': str(save_path),
                'filename': filename,
                'index': i
            })
            continue
        
        # Download image
        success = await download_image(url, save_path)
        if success and validate_image(save_path):
            downloaded_images.append({
                'original_url': url,
                'local_path': str(save_path),
                'filename': filename,
                'index': i
            })
        else:
            logger.error(f"Failed to download or validate image: {url}")
    
    return downloaded_images
