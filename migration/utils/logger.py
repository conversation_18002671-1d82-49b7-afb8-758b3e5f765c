"""Logging utilities for the migration tool."""

import sys
from pathlib import Path
from loguru import logger
from ..config import config

def setup_logger(name: str = "migration") -> None:
    """Setup logger with file and console output."""
    
    # Remove default handler
    logger.remove()
    
    # Console handler
    logger.add(
        sys.stdout,
        format=config.logging.format,
        level=config.logging.level,
        colorize=True
    )
    
    # File handler for all logs
    log_file = config.paths.logs_dir / f"{name}.log"
    logger.add(
        log_file,
        format=config.logging.format,
        level=config.logging.level,
        rotation=config.logging.rotation,
        retention=config.logging.retention,
        compression="zip"
    )
    
    # Error file handler
    error_log_file = config.paths.logs_dir / f"{name}_errors.log"
    logger.add(
        error_log_file,
        format=config.logging.format,
        level="ERROR",
        rotation=config.logging.rotation,
        retention=config.logging.retention,
        compression="zip"
    )

def get_logger(name: str = "migration"):
    """Get a logger instance with the specified name."""
    return logger.bind(name=name)

# Setup default logger
setup_logger()
