"""Command line interface for the migration tool."""

import asyncio
import click
from datetime import datetime
from pathlib import Path

from .config import config
from .utils.logger import get_logger, setup_logger
from .utils.file_utils import save_json, load_json
from .salla.exporter import SallaExporter
from .shopify.client import ShopifyClient
from .transformer.product_mapper import ProductMapper

# Setup logging
setup_logger("migration")
logger = get_logger("cli")

@click.group()
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
def cli(verbose):
    """Salla.sa to Shopify Migration Tool"""
    if verbose:
        config.logging.level = "DEBUG"
        setup_logger("migration")

@cli.command()
async def analyze():
    """Analyze APIs and generate migration plan."""
    logger.info("Starting API analysis...")
    
    # Test Salla.sa connection
    try:
        from .salla.client import SallaClient
        salla_client = SallaClient()
        
        # Test basic connectivity
        products_response = await salla_client.get_products(page=1, per_page=5)
        salla_status = "SUCCESS"
        salla_sample_count = len(products_response.get("data", []))
        
        logger.info(f"Salla.sa API: Connected successfully ({salla_sample_count} sample products)")
        
    except Exception as e:
        salla_status = "FAILED"
        salla_sample_count = 0
        logger.error(f"Salla.sa API connection failed: {str(e)}")
    
    # Test Shopify connection
    try:
        shopify_client = ShopifyClient()
        shopify_connected = await shopify_client.test_connection()
        shopify_status = "SUCCESS" if shopify_connected else "FAILED"
        
        if shopify_connected:
            products_response = await shopify_client.get_products(limit=5)
            shopify_sample_count = len(products_response.get("products", []))
            logger.info(f"Shopify API: Connected successfully ({shopify_sample_count} existing products)")
        else:
            shopify_sample_count = 0
            
    except Exception as e:
        shopify_status = "FAILED"
        shopify_sample_count = 0
        logger.error(f"Shopify API connection failed: {str(e)}")
    
    # Generate analysis report
    analysis_report = {
        "analysis_date": datetime.now().isoformat(),
        "salla": {
            "status": salla_status,
            "sample_products": salla_sample_count
        },
        "shopify": {
            "status": shopify_status,
            "existing_products": shopify_sample_count
        },
        "recommendations": []
    }
    
    # Add recommendations
    if salla_status == "SUCCESS" and shopify_status == "SUCCESS":
        analysis_report["recommendations"].append("✅ Both APIs are accessible - ready to proceed with migration")
    else:
        if salla_status == "FAILED":
            analysis_report["recommendations"].append("❌ Fix Salla.sa API credentials in .env file")
        if shopify_status == "FAILED":
            analysis_report["recommendations"].append("❌ Fix Shopify API credentials in .env file")
    
    # Save analysis report
    report_file = config.paths.data_dir / "api_analysis_report.json"
    save_json(analysis_report, report_file)
    
    # Print summary
    click.echo("\n" + "="*50)
    click.echo("API ANALYSIS SUMMARY")
    click.echo("="*50)
    click.echo(f"Salla.sa API: {salla_status}")
    click.echo(f"Shopify API: {shopify_status}")
    click.echo(f"Report saved: {report_file}")
    click.echo("="*50)

@cli.command()
@click.option('--download-images', is_flag=True, default=True, help='Download product images')
@click.option('--method', type=click.Choice(['api', 'scrape']), default='api', help='Export method: API or web scraping')
@click.option('--store-url', help='Store URL for scraping method (e.g., https://store.salla.sa)')
@click.option('--max-products', type=int, help='Maximum number of products to export (for testing)')
async def export(download_images, method, store_url, max_products):
    """Export data from Salla.sa."""
    logger.info(f"Starting Salla.sa data export using {method} method...")

    try:
        if method == 'scrape':
            if not store_url:
                # Try to get from config
                store_url = config.salla.store_url
                if not store_url:
                    click.echo("❌ Store URL is required for scraping method")
                    click.echo("Use --store-url option or set SALLA_STORE_URL in .env")
                    raise click.Abort()

            from .salla.scraper_exporter import SallaScraperExporter
            exporter = SallaScraperExporter(store_url)
            result = await exporter.export_all(max_products=max_products, download_images=download_images)
        else:
            # API method
            from .salla.exporter import SallaExporter
            exporter = SallaExporter()
            result = await exporter.export_all(download_images=download_images)
        
        click.echo("\n" + "="*50)
        click.echo("EXPORT COMPLETED")
        click.echo("="*50)
        click.echo(f"Products: {result['summary']['totals']['products']}")
        click.echo(f"Categories: {result['summary']['totals']['categories']}")
        click.echo(f"Brands: {result['summary']['totals']['brands']}")
        click.echo(f"Export file: {result['export_file']}")
        click.echo("="*50)
        
    except Exception as e:
        logger.error(f"Export failed: {str(e)}")
        click.echo(f"❌ Export failed: {str(e)}")
        raise click.Abort()

@cli.command()
@click.option('--input-file', help='Input export file (default: latest export)')
async def transform(input_file):
    """Transform Salla.sa data to Shopify format."""
    logger.info("Starting data transformation...")
    
    try:
        # Load export data
        if input_file:
            export_file = Path(input_file)
        else:
            export_file = config.paths.export_dir / "salla_export_latest.json"
        
        if not export_file.exists():
            click.echo(f"❌ Export file not found: {export_file}")
            click.echo("Run 'python -m migration.cli export' first")
            raise click.Abort()
        
        export_data = load_json(export_file)
        
        # Transform data
        mapper = ProductMapper()
        transformed_products = mapper.map_products(
            export_data["products"],
            export_data["categories"], 
            export_data["brands"]
        )
        
        # Save transformed data
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        transform_file = config.paths.import_dir / f"shopify_import_{timestamp}.json"
        
        transformed_data = {
            "metadata": {
                "transformation_date": datetime.now().isoformat(),
                "source_file": str(export_file),
                "total_products": len(transformed_products)
            },
            "products": transformed_products
        }
        
        save_json(transformed_data, transform_file)
        
        # Also save as latest
        latest_file = config.paths.import_dir / "shopify_import_latest.json"
        save_json(transformed_data, latest_file)
        
        click.echo("\n" + "="*50)
        click.echo("TRANSFORMATION COMPLETED")
        click.echo("="*50)
        click.echo(f"Transformed products: {len(transformed_products)}")
        click.echo(f"Output file: {transform_file}")
        click.echo("="*50)
        
    except Exception as e:
        logger.error(f"Transformation failed: {str(e)}")
        click.echo(f"❌ Transformation failed: {str(e)}")
        raise click.Abort()

@cli.command()
@click.option('--input-file', help='Input transformed file (default: latest)')
@click.option('--test', is_flag=True, default=True, help='Run in test mode (dry run)')
@click.option('--batch-size', default=10, help='Batch size for imports')
async def import_products(input_file, test, batch_size):
    """Import products to Shopify."""
    logger.info(f"Starting Shopify import (test mode: {test})...")
    
    try:
        # Load transformed data
        if input_file:
            import_file = Path(input_file)
        else:
            import_file = config.paths.import_dir / "shopify_import_latest.json"
        
        if not import_file.exists():
            click.echo(f"❌ Import file not found: {import_file}")
            click.echo("Run 'python -m migration.cli transform' first")
            raise click.Abort()
        
        import_data = load_json(import_file)
        products = import_data["products"]
        
        if test:
            click.echo(f"🧪 TEST MODE: Would import {len(products)} products")
            # Show sample of first few products
            for i, product in enumerate(products[:3]):
                title = product["product"]["title"]
                variants = len(product["product"]["variants"])
                images = len(product["product"]["images"])
                click.echo(f"  {i+1}. {title} ({variants} variants, {images} images)")
            
            if len(products) > 3:
                click.echo(f"  ... and {len(products) - 3} more products")
        else:
            # Actual import
            from .shopify.importer import ShopifyImporter
            importer = ShopifyImporter()
            result = await importer.import_products(products, batch_size=batch_size)
            
            click.echo("\n" + "="*50)
            click.echo("IMPORT COMPLETED")
            click.echo("="*50)
            click.echo(f"Successfully imported: {result['successful']}")
            click.echo(f"Failed imports: {result['failed']}")
            click.echo("="*50)
        
    except Exception as e:
        logger.error(f"Import failed: {str(e)}")
        click.echo(f"❌ Import failed: {str(e)}")
        raise click.Abort()

@cli.command()
@click.option('--production', is_flag=True, help='Run production migration (not test)')
@click.option('--method', type=click.Choice(['api', 'scrape']), default='api', help='Export method: API or web scraping')
@click.option('--store-url', help='Store URL for scraping method')
async def migrate(production, method, store_url):
    """Run complete migration process."""
    if production:
        click.confirm("⚠️  This will run a PRODUCTION migration. Continue?", abort=True)
        config.migration.test_mode = False

    logger.info(f"Starting complete migration (production: {production}, method: {method})...")

    try:
        # Step 1: Export
        click.echo(f"Step 1/3: Exporting from Salla.sa using {method} method...")

        if method == 'scrape':
            if not store_url:
                store_url = config.salla.store_url
                if not store_url:
                    click.echo("❌ Store URL is required for scraping method")
                    raise click.Abort()

            from .salla.scraper_exporter import SallaScraperExporter
            exporter = SallaScraperExporter(store_url)
            export_result = await exporter.export_all(download_images=True)
        else:
            from .salla.exporter import SallaExporter
            exporter = SallaExporter()
            export_result = await exporter.export_all(download_images=True)
        
        # Step 2: Transform
        click.echo("Step 2/3: Transforming data...")
        mapper = ProductMapper()
        transformed_products = mapper.map_products(
            export_result["data"]["products"],
            export_result["data"]["categories"],
            export_result["data"]["brands"]
        )
        
        # Step 3: Import
        click.echo("Step 3/3: Importing to Shopify...")
        if not production:
            click.echo(f"🧪 TEST MODE: Would import {len(transformed_products)} products")
        else:
            from .shopify.importer import ShopifyImporter
            importer = ShopifyImporter()
            import_result = await importer.import_products(transformed_products)
            
            click.echo("\n" + "="*50)
            click.echo("MIGRATION COMPLETED")
            click.echo("="*50)
            click.echo(f"Products exported: {export_result['summary']['totals']['products']}")
            click.echo(f"Products imported: {import_result['successful']}")
            click.echo(f"Failed imports: {import_result['failed']}")
            click.echo("="*50)
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        click.echo(f"❌ Migration failed: {str(e)}")
        raise click.Abort()

def main():
    """Main entry point."""
    # Check configuration
    errors = config.validate()
    if errors:
        click.echo("❌ Configuration errors:")
        for error in errors:
            click.echo(f"  - {error}")
        click.echo("\nPlease check your .env file and fix the issues above.")
        return
    
    # Run CLI with asyncio support
    def run_async(coro):
        return asyncio.run(coro)
    
    # Add async support to click commands
    for command in [analyze, export, transform, import_products, migrate]:
        command.callback = lambda func=command.callback: run_async(func())
    
    cli()

if __name__ == "__main__":
    main()
