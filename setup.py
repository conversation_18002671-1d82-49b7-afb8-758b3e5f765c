from setuptools import setup, find_packages

setup(
    name="salla-shopify-migration",
    version="1.0.0",
    description="Migration tool to export products from Salla.sa and import to Shopify",
    author="BSH Team",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "requests>=2.31.0",
        "python-dotenv>=1.0.0",
        "pandas>=2.1.4",
        "aiohttp>=3.9.1",
        "asyncio-throttle>=1.0.2",
        "Pillow>=10.1.0",
        "tqdm>=4.66.1",
        "click>=8.1.7",
        "pydantic>=2.5.2",
        "loguru>=0.7.2",
        "httpx>=0.25.2",
        "tenacity>=8.2.3",
        "jsonschema>=4.20.0",
        "openpyxl>=3.1.2",
        "beautifulsoup4>=4.12.2",
    ],
    python_requires=">=3.8",
    entry_points={
        "console_scripts": [
            "salla-migrate=migration.cli:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
)
