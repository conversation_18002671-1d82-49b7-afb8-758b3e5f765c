{"name": "salla-to-shopify-migration", "version": "1.0.0", "description": "Migration tool to export products from Salla.sa and import to Shopify", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "analyze": "node scripts/analyze-apis.js", "export": "node scripts/export-salla.js", "transform": "node scripts/transform-data.js", "import": "node scripts/import-shopify.js", "migrate": "node scripts/full-migration.js"}, "keywords": ["salla", "shopify", "migration", "ecommerce", "api"], "author": "BSH Team", "license": "MIT", "dependencies": {"axios": "^1.6.0", "dotenv": "^16.3.1", "fs-extra": "^11.1.1", "lodash": "^4.17.21", "moment": "^2.29.4", "p-limit": "^4.0.0", "progress": "^2.0.3", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}