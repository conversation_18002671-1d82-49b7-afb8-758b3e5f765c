const axios = require('axios');
const pLimit = require('p-limit');
const { createComponentLogger } = require('../utils/logger');
const config = require('../../config/config');

class ShopifyApiClient {
  constructor() {
    this.logger = createComponentLogger('ShopifyAPI');
    this.baseUrl = config.shopify.baseUrl;
    this.accessToken = config.shopify.accessToken;
    
    // Rate limiting - Shopify allows 2 requests per second
    this.limit = pLimit(config.shopify.rateLimit.requestsPerSecond);
    
    // Setup axios instance
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'X-Shopify-Access-Token': this.accessToken,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });

    // Add request/response interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`Response received: ${response.status} ${response.config.url}`);
        
        // Log rate limit headers
        if (response.headers['x-shopify-shop-api-call-limit']) {
          this.logger.debug(`API Call Limit: ${response.headers['x-shopify-shop-api-call-limit']}`);
        }
        
        return response;
      },
      (error) => {
        this.logger.error('Response error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  async makeRequest(method, endpoint, data = null, params = {}) {
    return this.limit(async () => {
      try {
        const response = await this.client({
          method,
          url: endpoint,
          data,
          params
        });
        return response.data;
      } catch (error) {
        this.logger.error(`API request failed: ${method} ${endpoint}`, error);
        throw error;
      }
    });
  }

  // Product-related methods
  async createProduct(productData) {
    this.logger.info(`Creating product: ${productData.product.title}`);
    return this.makeRequest('POST', '/products.json', productData);
  }

  async updateProduct(productId, productData) {
    this.logger.info(`Updating product: ${productId}`);
    return this.makeRequest('PUT', `/products/${productId}.json`, productData);
  }

  async getProducts(limit = 50, sinceId = null) {
    this.logger.info(`Fetching products (limit: ${limit})`);
    const params = { limit };
    if (sinceId) params.since_id = sinceId;
    
    return this.makeRequest('GET', '/products.json', null, params);
  }

  async getProduct(productId) {
    this.logger.info(`Fetching product ${productId}`);
    return this.makeRequest('GET', `/products/${productId}.json`);
  }

  // Image-related methods
  async createProductImage(productId, imageData) {
    this.logger.info(`Adding image to product ${productId}`);
    return this.makeRequest('POST', `/products/${productId}/images.json`, { image: imageData });
  }

  // Collection-related methods
  async getCollections(limit = 50) {
    this.logger.info(`Fetching collections (limit: ${limit})`);
    return this.makeRequest('GET', '/collections.json', null, { limit });
  }

  async createCollection(collectionData) {
    this.logger.info(`Creating collection: ${collectionData.collection.title}`);
    return this.makeRequest('POST', '/collections.json', collectionData);
  }

  // Bulk operations
  async createBulkOperation(query, type = 'mutation') {
    this.logger.info(`Creating bulk operation: ${type}`);
    const mutation = `
      mutation {
        bulkOperationRunQuery(
          query: """${query}"""
        ) {
          bulkOperation {
            id
            status
          }
          userErrors {
            field
            message
          }
        }
      }
    `;
    
    return this.makeRequest('POST', '/graphql.json', { query: mutation });
  }

  async getBulkOperationStatus(operationId) {
    this.logger.info(`Checking bulk operation status: ${operationId}`);
    const query = `
      query {
        node(id: "${operationId}") {
          ... on BulkOperation {
            id
            status
            errorCode
            createdAt
            completedAt
            objectCount
            fileSize
            url
            partialDataUrl
          }
        }
      }
    `;
    
    return this.makeRequest('POST', '/graphql.json', { query });
  }

  // Utility methods
  async getAllProducts() {
    const allProducts = [];
    let sinceId = null;
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await this.getProducts(250, sinceId);
        
        if (response.products && response.products.length > 0) {
          allProducts.push(...response.products);
          sinceId = response.products[response.products.length - 1].id;
          hasMore = response.products.length === 250;
        } else {
          hasMore = false;
        }
      } catch (error) {
        this.logger.error('Error fetching products:', error);
        throw error;
      }
    }

    this.logger.info(`Fetched total of ${allProducts.length} products`);
    return allProducts;
  }
}

module.exports = ShopifyApiClient;
