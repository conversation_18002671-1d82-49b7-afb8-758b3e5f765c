const SallaApiClient = require('./api-client');
const { createComponentLogger } = require('../utils/logger');
const fs = require('fs-extra');
const path = require('path');
const config = require('../../config/config');

class SallaProductExporter {
  constructor() {
    this.logger = createComponentLogger('SallaExporter');
    this.client = new SallaApiClient();
    this.exportData = {
      products: [],
      categories: [],
      brands: [],
      metadata: {
        exportDate: new Date().toISOString(),
        totalProducts: 0,
        totalCategories: 0,
        totalBrands: 0
      }
    };
  }

  async exportAllProducts() {
    this.logger.info('Starting product export from Salla.sa...');
    
    try {
      // Export products with all details
      this.exportData.products = await this.client.getAllPages(this.client.getProducts, 1, 50);
      
      // Enrich products with additional data
      for (let i = 0; i < this.exportData.products.length; i++) {
        const product = this.exportData.products[i];
        this.logger.info(`Enriching product ${i + 1}/${this.exportData.products.length}: ${product.name}`);
        
        try {
          // Get detailed product info
          const detailedProduct = await this.client.getProduct(product.id);
          this.exportData.products[i] = { ...product, ...detailedProduct.data };
          
          // Get product images
          const images = await this.client.getProductImages(product.id);
          if (images.data) {
            this.exportData.products[i].images = images.data;
          }
        } catch (error) {
          this.logger.warn(`Failed to enrich product ${product.id}:`, error.message);
        }
      }
      
      this.exportData.metadata.totalProducts = this.exportData.products.length;
      this.logger.info(`Exported ${this.exportData.products.length} products`);
      
    } catch (error) {
      this.logger.error('Failed to export products:', error);
      throw error;
    }
  }

  async exportCategories() {
    this.logger.info('Exporting categories from Salla.sa...');
    
    try {
      this.exportData.categories = await this.client.getAllPages(this.client.getCategories, 1, 50);
      this.exportData.metadata.totalCategories = this.exportData.categories.length;
      this.logger.info(`Exported ${this.exportData.categories.length} categories`);
    } catch (error) {
      this.logger.error('Failed to export categories:', error);
      throw error;
    }
  }

  async exportBrands() {
    this.logger.info('Exporting brands from Salla.sa...');
    
    try {
      this.exportData.brands = await this.client.getAllPages(this.client.getBrands, 1, 50);
      this.exportData.metadata.totalBrands = this.exportData.brands.length;
      this.logger.info(`Exported ${this.exportData.brands.length} brands`);
    } catch (error) {
      this.logger.error('Failed to export brands:', error);
      throw error;
    }
  }

  async saveExportData() {
    this.logger.info('Saving export data...');
    
    await fs.ensureDir(config.paths.exportDir);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const exportPath = path.join(config.paths.exportDir, `salla-export-${timestamp}.json`);
    
    await fs.writeJson(exportPath, this.exportData, { spaces: 2 });
    
    // Also save a latest version
    const latestPath = path.join(config.paths.exportDir, 'salla-export-latest.json');
    await fs.writeJson(latestPath, this.exportData, { spaces: 2 });
    
    this.logger.info(`Export data saved to: ${exportPath}`);
    return exportPath;
  }

  async generateExportSummary() {
    const summary = {
      exportDate: this.exportData.metadata.exportDate,
      totals: {
        products: this.exportData.metadata.totalProducts,
        categories: this.exportData.metadata.totalCategories,
        brands: this.exportData.metadata.totalBrands
      },
      productSample: this.exportData.products.slice(0, 3).map(p => ({
        id: p.id,
        name: p.name,
        price: p.price,
        hasImages: p.images && p.images.length > 0,
        hasVariants: p.variants && p.variants.length > 0
      })),
      categorySample: this.exportData.categories.slice(0, 5).map(c => ({
        id: c.id,
        name: c.name
      })),
      brandSample: this.exportData.brands.slice(0, 5).map(b => ({
        id: b.id,
        name: b.name
      }))
    };

    const summaryPath = path.join(config.paths.exportDir, 'export-summary.json');
    await fs.writeJson(summaryPath, summary, { spaces: 2 });
    
    return summary;
  }

  async run() {
    this.logger.info('Starting complete Salla.sa data export...');
    
    try {
      await this.exportAllProducts();
      await this.exportCategories();
      await this.exportBrands();
      
      const exportPath = await this.saveExportData();
      const summary = await this.generateExportSummary();
      
      this.logger.info('Export completed successfully!');
      console.log('\n' + '='.repeat(50));
      console.log('SALLA.SA EXPORT SUMMARY');
      console.log('='.repeat(50));
      console.log(`Products: ${summary.totals.products}`);
      console.log(`Categories: ${summary.totals.categories}`);
      console.log(`Brands: ${summary.totals.brands}`);
      console.log(`Export file: ${exportPath}`);
      console.log('='.repeat(50));
      
      return { exportPath, summary };
      
    } catch (error) {
      this.logger.error('Export failed:', error);
      throw error;
    }
  }
}

module.exports = SallaProductExporter;
