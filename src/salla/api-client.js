const axios = require('axios');
const pLimit = require('p-limit');
const { createComponentLogger } = require('../utils/logger');
const config = require('../../config/config');

class SallaApiClient {
  constructor() {
    this.logger = createComponentLogger('SallaAPI');
    this.baseUrl = config.salla.baseUrl;
    this.accessToken = config.salla.accessToken;
    
    // Rate limiting
    this.limit = pLimit(config.salla.rateLimit.requestsPerSecond);
    
    // Setup axios instance
    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      timeout: 30000
    });

    // Add request/response interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`Making request to: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        this.logger.error('Request error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`Response received: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        this.logger.error('Response error:', {
          status: error.response?.status,
          statusText: error.response?.statusText,
          url: error.config?.url,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  async makeRequest(method, endpoint, data = null, params = {}) {
    return this.limit(async () => {
      try {
        const response = await this.client({
          method,
          url: endpoint,
          data,
          params
        });
        return response.data;
      } catch (error) {
        this.logger.error(`API request failed: ${method} ${endpoint}`, error);
        throw error;
      }
    });
  }

  // Product-related methods
  async getProducts(page = 1, perPage = 50) {
    this.logger.info(`Fetching products page ${page}`);
    return this.makeRequest('GET', '/products', null, {
      page,
      per_page: perPage
    });
  }

  async getProduct(productId) {
    this.logger.info(`Fetching product ${productId}`);
    return this.makeRequest('GET', `/products/${productId}`);
  }

  async getProductImages(productId) {
    this.logger.info(`Fetching images for product ${productId}`);
    return this.makeRequest('GET', `/products/${productId}/images`);
  }

  // Category-related methods
  async getCategories(page = 1, perPage = 50) {
    this.logger.info(`Fetching categories page ${page}`);
    return this.makeRequest('GET', '/categories', null, {
      page,
      per_page: perPage
    });
  }

  // Brand-related methods
  async getBrands(page = 1, perPage = 50) {
    this.logger.info(`Fetching brands page ${page}`);
    return this.makeRequest('GET', '/brands', null, {
      page,
      per_page: perPage
    });
  }

  // Utility methods
  async getAllPages(fetchFunction, ...args) {
    const allData = [];
    let page = 1;
    let hasMore = true;

    while (hasMore) {
      try {
        const response = await fetchFunction.call(this, ...args, page);
        
        if (response.data && Array.isArray(response.data)) {
          allData.push(...response.data);
          
          // Check if there are more pages
          hasMore = response.pagination && 
                   response.pagination.current_page < response.pagination.last_page;
          page++;
        } else {
          hasMore = false;
        }
      } catch (error) {
        this.logger.error(`Error fetching page ${page}:`, error);
        throw error;
      }
    }

    this.logger.info(`Fetched total of ${allData.length} items`);
    return allData;
  }
}

module.exports = SallaApiClient;
