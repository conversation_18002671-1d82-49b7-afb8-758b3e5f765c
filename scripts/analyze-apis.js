#!/usr/bin/env node

const SallaApiClient = require('../src/salla/api-client');
const ShopifyApiClient = require('../src/shopify/api-client');
const { createComponentLogger } = require('../src/utils/logger');
const fs = require('fs-extra');
const path = require('path');

const logger = createComponentLogger('APIAnalyzer');

class ApiAnalyzer {
  constructor() {
    this.sallaClient = new SallaApiClient();
    this.shopifyClient = new ShopifyApiClient();
    this.analysisResults = {
      salla: {},
      shopify: {},
      mapping: {},
      challenges: []
    };
  }

  async analyzeSallaApi() {
    logger.info('Analyzing Salla.sa API...');
    
    try {
      // Test basic connectivity
      const products = await this.sallaClient.getProducts(1, 5);
      this.analysisResults.salla.connectivity = 'SUCCESS';
      this.analysisResults.salla.sampleProductCount = products.data?.length || 0;
      
      if (products.data && products.data.length > 0) {
        this.analysisResults.salla.sampleProduct = products.data[0];
        this.analysisResults.salla.productFields = Object.keys(products.data[0]);
      }

      // Test categories
      const categories = await this.sallaClient.getCategories(1, 5);
      this.analysisResults.salla.sampleCategoryCount = categories.data?.length || 0;
      if (categories.data && categories.data.length > 0) {
        this.analysisResults.salla.sampleCategory = categories.data[0];
      }

      // Test brands
      const brands = await this.sallaClient.getBrands(1, 5);
      this.analysisResults.salla.sampleBrandCount = brands.data?.length || 0;
      if (brands.data && brands.data.length > 0) {
        this.analysisResults.salla.sampleBrand = brands.data[0];
      }

      logger.info('Salla.sa API analysis completed successfully');
    } catch (error) {
      logger.error('Salla.sa API analysis failed:', error);
      this.analysisResults.salla.connectivity = 'FAILED';
      this.analysisResults.salla.error = error.message;
    }
  }

  async analyzeShopifyApi() {
    logger.info('Analyzing Shopify API...');
    
    try {
      // Test basic connectivity
      const products = await this.shopifyClient.getProducts(5);
      this.analysisResults.shopify.connectivity = 'SUCCESS';
      this.analysisResults.shopify.existingProductCount = products.products?.length || 0;
      
      if (products.products && products.products.length > 0) {
        this.analysisResults.shopify.sampleProduct = products.products[0];
        this.analysisResults.shopify.productFields = Object.keys(products.products[0]);
      }

      // Test collections
      const collections = await this.shopifyClient.getCollections(5);
      this.analysisResults.shopify.existingCollectionCount = collections.collections?.length || 0;
      if (collections.collections && collections.collections.length > 0) {
        this.analysisResults.shopify.sampleCollection = collections.collections[0];
      }

      logger.info('Shopify API analysis completed successfully');
    } catch (error) {
      logger.error('Shopify API analysis failed:', error);
      this.analysisResults.shopify.connectivity = 'FAILED';
      this.analysisResults.shopify.error = error.message;
    }
  }

  analyzeFieldMapping() {
    logger.info('Analyzing field mapping...');
    
    if (!this.analysisResults.salla.sampleProduct || !this.analysisResults.shopify.sampleProduct) {
      logger.warn('Cannot analyze field mapping - missing sample data');
      return;
    }

    const sallaFields = this.analysisResults.salla.productFields;
    const shopifyFields = this.analysisResults.shopify.productFields;

    // Define field mappings
    const fieldMappings = {
      'name': 'title',
      'description': 'body_html',
      'price': 'variants[0].price',
      'sale_price': 'variants[0].compare_at_price',
      'sku': 'variants[0].sku',
      'quantity': 'variants[0].inventory_quantity',
      'status': 'status',
      'images': 'images',
      'categories': 'product_type + collections',
      'brand_id': 'vendor',
      'seo.title': 'seo_title',
      'seo.description': 'seo_description'
    };

    this.analysisResults.mapping = {
      fieldMappings,
      sallaFields,
      shopifyFields,
      unmappedSallaFields: sallaFields.filter(field => !fieldMappings[field]),
      requiredShopifyFields: ['title', 'variants']
    };
  }

  identifyChallenges() {
    logger.info('Identifying migration challenges...');
    
    const challenges = [];

    // Image handling
    if (this.analysisResults.salla.sampleProduct?.images) {
      challenges.push({
        type: 'IMAGE_MIGRATION',
        description: 'Images need to be downloaded from Salla.sa and uploaded to Shopify',
        complexity: 'MEDIUM',
        solution: 'Implement image download and upload pipeline'
      });
    }

    // Variant structure differences
    challenges.push({
      type: 'VARIANT_STRUCTURE',
      description: 'Different variant handling between platforms',
      complexity: 'HIGH',
      solution: 'Transform Salla.sa variants to Shopify variant format'
    });

    // Category to collection mapping
    if (this.analysisResults.salla.sampleCategoryCount > 0) {
      challenges.push({
        type: 'CATEGORY_MAPPING',
        description: 'Salla.sa categories need to be mapped to Shopify collections',
        complexity: 'MEDIUM',
        solution: 'Create category-to-collection mapping and auto-create collections'
      });
    }

    // SEO URL handling
    challenges.push({
      type: 'SEO_URLS',
      description: 'Handle generation and conflicts',
      complexity: 'LOW',
      solution: 'Generate unique handles and implement conflict resolution'
    });

    // Currency and pricing
    challenges.push({
      type: 'PRICING_FORMAT',
      description: 'Currency formatting and decimal handling',
      complexity: 'LOW',
      solution: 'Standardize price format to string with proper decimal places'
    });

    this.analysisResults.challenges = challenges;
  }

  async generateReport() {
    logger.info('Generating analysis report...');
    
    const reportPath = path.join('./data', 'api-analysis-report.json');
    await fs.ensureDir('./data');
    await fs.writeJson(reportPath, this.analysisResults, { spaces: 2 });
    
    // Generate human-readable summary
    const summary = this.generateSummary();
    const summaryPath = path.join('./data', 'api-analysis-summary.md');
    await fs.writeFile(summaryPath, summary);
    
    logger.info(`Analysis report saved to: ${reportPath}`);
    logger.info(`Analysis summary saved to: ${summaryPath}`);
  }

  generateSummary() {
    const { salla, shopify, mapping, challenges } = this.analysisResults;
    
    return `# API Analysis Summary

## Salla.sa API Status
- **Connectivity**: ${salla.connectivity}
- **Sample Products**: ${salla.sampleProductCount || 0}
- **Sample Categories**: ${salla.sampleCategoryCount || 0}
- **Sample Brands**: ${salla.sampleBrandCount || 0}
${salla.error ? `- **Error**: ${salla.error}` : ''}

## Shopify API Status
- **Connectivity**: ${shopify.connectivity}
- **Existing Products**: ${shopify.existingProductCount || 0}
- **Existing Collections**: ${shopify.existingCollectionCount || 0}
${shopify.error ? `- **Error**: ${shopify.error}` : ''}

## Field Mapping Analysis
- **Mapped Fields**: ${Object.keys(mapping.fieldMappings || {}).length}
- **Unmapped Salla Fields**: ${(mapping.unmappedSallaFields || []).join(', ')}
- **Required Shopify Fields**: ${(mapping.requiredShopifyFields || []).join(', ')}

## Migration Challenges (${challenges.length})
${challenges.map(challenge => 
  `### ${challenge.type} (${challenge.complexity})
- **Description**: ${challenge.description}
- **Solution**: ${challenge.solution}
`).join('\n')}

## Next Steps
1. Resolve any API connectivity issues
2. Implement field mapping transformations
3. Address identified challenges
4. Create test migration with sample data
5. Validate data integrity and completeness

Generated on: ${new Date().toISOString()}
`;
  }

  async run() {
    logger.info('Starting API analysis...');
    
    await this.analyzeSallaApi();
    await this.analyzeShopifyApi();
    this.analyzeFieldMapping();
    this.identifyChallenges();
    await this.generateReport();
    
    logger.info('API analysis completed!');
    
    // Print summary to console
    console.log('\n' + '='.repeat(50));
    console.log('API ANALYSIS SUMMARY');
    console.log('='.repeat(50));
    console.log(`Salla.sa API: ${this.analysisResults.salla.connectivity}`);
    console.log(`Shopify API: ${this.analysisResults.shopify.connectivity}`);
    console.log(`Challenges Identified: ${this.analysisResults.challenges.length}`);
    console.log('='.repeat(50));
  }
}

// Run the analyzer if called directly
if (require.main === module) {
  const analyzer = new ApiAnalyzer();
  analyzer.run().catch(error => {
    logger.error('Analysis failed:', error);
    process.exit(1);
  });
}

module.exports = ApiAnalyzer;
