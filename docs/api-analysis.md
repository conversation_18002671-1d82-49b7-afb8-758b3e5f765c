# API Analysis: Salla.sa vs Shopify

## Salla.sa API Analysis

### Authentication
- **Method**: OAuth 2.0 or API Key
- **Base URL**: `https://api.salla.dev/admin/v2/`
- **Documentation**: https://docs.salla.dev/

### Key Endpoints for Product Export
```
GET /products                    # List all products
GET /products/{id}              # Get specific product
GET /products/{id}/images       # Get product images
GET /categories                 # List categories
GET /brands                     # List brands
```

### Product Data Structure (Salla.sa)
```json
{
  "id": "string",
  "name": "string",
  "description": "string",
  "price": "number",
  "sale_price": "number",
  "sku": "string",
  "quantity": "number",
  "status": "active|inactive",
  "images": [
    {
      "id": "string",
      "url": "string",
      "alt": "string"
    }
  ],
  "categories": ["category_id"],
  "brand_id": "string",
  "variants": [
    {
      "id": "string",
      "name": "string",
      "price": "number",
      "sku": "string",
      "quantity": "number"
    }
  ],
  "seo": {
    "title": "string",
    "description": "string"
  }
}
```

## Shopify API Analysis

### Authentication
- **Method**: Private App or OAuth 2.0
- **Base URL**: `https://{shop}.myshopify.com/admin/api/2023-10/`
- **Documentation**: https://shopify.dev/docs/api/admin-rest

### Key Endpoints for Product Import
```
POST /products.json             # Create product
PUT /products/{id}.json         # Update product
POST /products/{id}/images.json # Add product images
GET /collections.json           # List collections
POST /collections.json          # Create collection
```

### Product Data Structure (Shopify)
```json
{
  "product": {
    "title": "string",
    "body_html": "string",
    "vendor": "string",
    "product_type": "string",
    "handle": "string",
    "status": "active|draft|archived",
    "tags": "string",
    "variants": [
      {
        "title": "string",
        "price": "string",
        "sku": "string",
        "inventory_quantity": "number",
        "weight": "number",
        "weight_unit": "kg"
      }
    ],
    "images": [
      {
        "src": "string",
        "alt": "string"
      }
    ],
    "seo_title": "string",
    "seo_description": "string"
  }
}
```

## Data Mapping Strategy

### Field Mappings
| Salla.sa Field | Shopify Field | Transformation |
|----------------|---------------|----------------|
| `name` | `title` | Direct mapping |
| `description` | `body_html` | HTML formatting |
| `price` | `variants[0].price` | String conversion |
| `sale_price` | `variants[0].compare_at_price` | String conversion |
| `sku` | `variants[0].sku` | Direct mapping |
| `quantity` | `variants[0].inventory_quantity` | Direct mapping |
| `status` | `status` | Map active/inactive to active/draft |
| `categories` | `product_type` + collections | Category to collection mapping |
| `brand_id` | `vendor` | Brand name lookup |
| `seo.title` | `seo_title` | Direct mapping |
| `seo.description` | `seo_description` | Direct mapping |

### Challenges Identified
1. **Image URLs**: Need to download from Salla.sa and upload to Shopify
2. **Variants**: Different variant structure requires transformation
3. **Categories**: Salla.sa categories → Shopify collections + product_type
4. **Pricing**: Currency and formatting considerations
5. **SEO URLs**: Handle generation and conflicts
6. **Inventory**: Stock management differences

## Rate Limits
- **Salla.sa**: 1000 requests per hour (to be verified)
- **Shopify**: 2 requests per second (REST API), 1000 operations per bulk job

## Next Steps
1. Verify API credentials and access
2. Test basic API calls
3. Create detailed field mapping document
4. Identify custom fields and metadata handling
