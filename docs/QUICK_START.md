# Quick Start Guide

## 🚀 Get Started in 5 Minutes

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure API Credentials
```bash
cp .env.example .env
```

Edit `.env` file with your credentials:
```env
# Salla.sa API
SALLA_ACCESS_TOKEN=your_salla_access_token

# Shopify API  
SHOPIFY_SHOP_NAME=your-shop-name
SHOPIFY_ACCESS_TOKEN=your_shopify_access_token
```

### 3. Run Migration

#### Option A: Simple Script (Recommended)
```bash
python run_migration.py
```

#### Option B: Step by Step
```bash
# Test API connections
python -m migration.cli analyze

# Export from Salla.sa
python -m migration.cli export

# Transform data
python -m migration.cli transform

# Import to Shopify (test mode)
python -m migration.cli import --test

# Production import
python -m migration.cli import --production
```

## 🔧 Configuration Options

### Environment Variables
- `MIGRATION_TEST_MODE=true` - Run in test mode (no actual imports)
- `MIGRATION_BATCH_SIZE=50` - Number of products per batch
- `MIGRATION_ENABLE_IMAGE_MIGRATION=true` - Download and process images
- `LOG_LEVEL=INFO` - Logging level (DEBUG, INFO, WARNING, ERROR)

### Test vs Production Mode
- **Test Mode**: Simulates import, shows what would be imported
- **Production Mode**: Actually creates products in Shopify

## 📊 What Gets Migrated

✅ **Products**
- Title, description, pricing
- SKUs and inventory
- Product variants
- SEO metadata

✅ **Images**
- Downloads from Salla.sa
- Optimizes for web
- Associates with products

✅ **Categories → Collections**
- Maps categories to Shopify collections
- Creates collections automatically

✅ **Brands → Vendors**
- Maps brand information to vendor field

## 🔍 Monitoring Progress

The tool provides detailed logging and progress bars:
- Real-time progress indicators
- Detailed logs in `logs/` directory
- Export/import summaries
- Error tracking and reporting

## 📁 Output Files

After running, you'll find:
- `data/exports/` - Exported Salla.sa data
- `data/imports/` - Transformed Shopify data
- `data/images/` - Downloaded product images
- `logs/` - Detailed operation logs

## 🆘 Troubleshooting

### Common Issues

**API Connection Failed**
- Check your API credentials in `.env`
- Verify API permissions and scopes
- Check network connectivity

**Import Errors**
- Review error logs in `logs/migration_errors.log`
- Check Shopify API rate limits
- Verify product data format

**Image Download Issues**
- Check image URLs are accessible
- Verify sufficient disk space
- Review image format compatibility

### Getting Help
1. Check the logs in `logs/` directory
2. Review the troubleshooting guide
3. Run with `--verbose` flag for detailed output

## 🎯 Next Steps

After successful migration:
1. Review imported products in Shopify admin
2. Test product pages and checkout
3. Update DNS settings to point to Shopify
4. Setup URL redirects from old Salla.sa URLs
5. Clean up temporary files and logs
