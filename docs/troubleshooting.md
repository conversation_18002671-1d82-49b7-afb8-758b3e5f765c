# Troubleshooting Guide

## 🔧 Common Issues and Solutions

### API Connection Issues

#### Salla.sa API Connection Failed
**Error**: `Salla.sa API connection failed: HTTP 401`

**Solutions**:
1. Check your `SALLA_ACCESS_TOKEN` in `.env` file
2. Verify the token hasn't expired
3. Ensure you have the correct API permissions
4. Test the token manually with curl:
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" https://api.salla.dev/admin/v2/products?per_page=1
   ```

#### Shopify API Connection Failed
**Error**: `Shopify API connection failed: HTTP 401`

**Solutions**:
1. Verify `SHOPIFY_SHOP_NAME` and `SHOPIFY_ACCESS_TOKEN` in `.env`
2. Check that your private app has the required permissions:
   - `read_products`, `write_products`
   - `read_product_listings`, `write_product_listings`
   - `read_collections`, `write_collections`
3. Test the connection:
   ```bash
   curl -H "X-Shopify-Access-Token: YOUR_TOKEN" https://YOUR_SHOP.myshopify.com/admin/api/2023-10/products.json?limit=1
   ```

### Data Export Issues

#### No Products Found
**Error**: `Found 0 products to export`

**Solutions**:
1. Check if your Salla.sa store has products
2. Verify API permissions include product access
3. Check if products are published/active

#### Image Download Failures
**Error**: `Failed to download image: HTTP 404`

**Solutions**:
1. Check if image URLs are publicly accessible
2. Verify sufficient disk space in `data/images/` directory
3. Check network connectivity and firewall settings
4. Some images might be behind authentication - these will be skipped

### Data Transformation Issues

#### Product Mapping Errors
**Error**: `Error mapping product: KeyError`

**Solutions**:
1. Check if required fields exist in Salla.sa data
2. Review the product data structure in export files
3. Update field mappings in `ProductMapper` if needed

#### Invalid Product Data
**Error**: `Shopify validation error: Title can't be blank`

**Solutions**:
1. Ensure all products have required fields (title, price)
2. Check for special characters in product names
3. Verify price formats are valid numbers

### Import Issues

#### Rate Limit Exceeded
**Error**: `HTTP 429: Too Many Requests`

**Solutions**:
1. The tool has built-in rate limiting, but you can:
2. Reduce `MIGRATION_BATCH_SIZE` in `.env`
3. Add delays between batches
4. Check if other apps are using Shopify API simultaneously

#### Product Creation Failed
**Error**: `Failed to create product: Validation error`

**Solutions**:
1. Check Shopify's product requirements
2. Verify variant data is properly formatted
3. Ensure SKUs are unique
4. Check for duplicate product handles

#### Bulk Import Timeout
**Error**: `Bulk operation timeout`

**Solutions**:
1. Reduce batch size
2. Split large imports into smaller chunks
3. Use the step-by-step approach instead of full migration

### Performance Issues

#### Slow Export Process
**Solutions**:
1. Check network speed to Salla.sa
2. Reduce concurrent requests
3. Skip image downloads for faster export:
   ```bash
   python -m migration.cli export --no-images
   ```

#### High Memory Usage
**Solutions**:
1. Process products in smaller batches
2. Clear processed data periodically
3. Increase system memory if possible

#### Disk Space Issues
**Solutions**:
1. Clean up old export/import files
2. Disable image downloads if not needed
3. Use image optimization to reduce file sizes

## 🔍 Debugging Steps

### 1. Enable Verbose Logging
```bash
python -m migration.cli --verbose analyze
```

### 2. Check Log Files
```bash
# View recent logs
tail -f logs/migration.log

# Check error logs
cat logs/migration_errors.log
```

### 3. Test Individual Components
```bash
# Test Salla.sa connection only
python -c "
import asyncio
from migration.salla.client import SallaClient
client = SallaClient()
result = asyncio.run(client.get_products(1, 1))
print('Success:', len(result.get('data', [])), 'products')
"

# Test Shopify connection only
python -c "
import asyncio
from migration.shopify.client import ShopifyClient
client = ShopifyClient()
result = asyncio.run(client.test_connection())
print('Shopify connection:', result)
"
```

### 4. Validate Configuration
```bash
python -c "
from migration.config import config
errors = config.validate()
if errors:
    print('Config errors:', errors)
else:
    print('Configuration is valid')
"
```

## 📊 Data Validation

### Check Export Data
```bash
# View export summary
cat data/exports/export_summary_*.json

# Count products in export
python -c "
from migration.utils.file_utils import load_json
data = load_json('data/exports/salla_export_latest.json')
print(f'Products: {len(data[\"products\"])}')
print(f'Categories: {len(data[\"categories\"])}')
print(f'Brands: {len(data[\"brands\"])}')
"
```

### Validate Transformed Data
```bash
# Check transformed products
python -c "
from migration.utils.file_utils import load_json
data = load_json('data/imports/shopify_import_latest.json')
products = data['products']
print(f'Transformed products: {len(products)}')
for i, p in enumerate(products[:3]):
    title = p['product']['title']
    variants = len(p['product']['variants'])
    print(f'{i+1}. {title} ({variants} variants)')
"
```

## 🆘 Getting Additional Help

### Log Analysis
The most important log files:
- `logs/migration.log` - General operation logs
- `logs/migration_errors.log` - Error-specific logs
- `data/import_results_*.json` - Import success/failure details

### Contact Information
If you continue to experience issues:
1. Check the GitHub repository for known issues
2. Review the API documentation for both platforms
3. Consider reaching out to Salla.sa or Shopify support for API-specific issues

### Useful Commands for Debugging
```bash
# Check Python environment
python --version
pip list | grep -E "(requests|httpx|aiohttp)"

# Check disk space
df -h

# Check network connectivity
ping api.salla.dev
ping your-shop.myshopify.com

# Validate JSON files
python -m json.tool data/exports/salla_export_latest.json > /dev/null && echo "Valid JSON" || echo "Invalid JSON"
```
